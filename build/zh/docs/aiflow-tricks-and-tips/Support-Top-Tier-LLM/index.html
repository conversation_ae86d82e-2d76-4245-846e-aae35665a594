<!doctype html>
<html lang="zh" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Support-Top-Tier-LLM" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Multi-LLM Support | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Multi-LLM Support | FunBlocks AI"><meta data-rh="true" name="description" content="In today&#x27;s rapidly evolving AI landscape, having access to the right language model for your specific needs can make all the difference in productivity and results. FunBlocks AIFlow stands out by offering comprehensive support for multiple leading LLM models, giving you unprecedented flexibility and power in your workflows."><meta data-rh="true" property="og:description" content="In today&#x27;s rapidly evolving AI landscape, having access to the right language model for your specific needs can make all the difference in productivity and results. FunBlocks AIFlow stands out by offering comprehensive support for multiple leading LLM models, giving you unprecedented flexibility and power in your workflows."><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.2d3bf4a8.css">
<script src="/zh/assets/js/runtime~main.ce179126.js" defer="defer"></script>
<script src="/zh/assets/js/main.57960d62.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/aiflow_llm_dropdown_menu.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="回到顶部" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="文档侧边栏" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/zh/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="展开侧边栏分类 &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/zh/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="折叠侧边栏分类 &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/ai-tools">AI Tools</a><button aria-label="展开侧边栏分类 &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="页面路径"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="主页面" class="breadcrumbs__link" href="/zh/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/zh/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Multi-LLM Support</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">本页总览</button></div><div class="theme-doc-markdown markdown"><header><h1>Multi-LLM Support</h1></header>
<p>In today&#x27;s rapidly evolving AI landscape, having access to the right language model for your specific needs can make all the difference in productivity and results. FunBlocks AIFlow stands out by offering comprehensive support for multiple leading LLM models, giving you unprecedented flexibility and power in your workflows.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="official-support-for-top-tier-llm-models">Official Support for Top-Tier LLM Models<a href="#official-support-for-top-tier-llm-models" class="hash-link" aria-label="Official Support for Top-Tier LLM Models的直接链接" title="Official Support for Top-Tier LLM Models的直接链接">​</a></h2>
<p>FunBlocks AI seamlessly integrates with all leading large language models, including OpenAI&#x27;s GPT-4o, Anthropic&#x27;s Claude, Google&#x27;s Gemini, DeepSeek, and other state-of-the-art AI models. Additionally, FunBlocks AI will continuously update to the latest and most powerful versions available.</p>
<img src="/img/portfolio/fullsize/aiflow_llm_dropdown_menu.png" width="640" alt="FunBlocks AIFlow AI models dropdown menu">
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="why-access-to-multiple-models-matters">Why Access to Multiple Models Matters<a href="#why-access-to-multiple-models-matters" class="hash-link" aria-label="Why Access to Multiple Models Matters的直接链接" title="Why Access to Multiple Models Matters的直接链接">​</a></h3>
<p>Having multiple AI models at your fingertips isn&#x27;t just a luxury—it&#x27;s a strategic advantage:</p>
<ul>
<li>
<p><strong>Leverage specialized capabilities</strong>: Each model excels in different areas. GPT-4o might handle certain creative tasks brilliantly, while Claude might offer superior reasoning for analytical problems.</p>
</li>
<li>
<p><strong>Match models to specific requirements</strong>: Choose the perfect AI partner based on your unique task requirements rather than forcing all your diverse needs through a single model.</p>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="key-benefits-of-funblocks-multi-model-approach">Key Benefits of FunBlocks&#x27; Multi-Model Approach<a href="#key-benefits-of-funblocks-multi-model-approach" class="hash-link" aria-label="Key Benefits of FunBlocks&#x27; Multi-Model Approach的直接链接" title="Key Benefits of FunBlocks&#x27; Multi-Model Approach的直接链接">​</a></h3>
<ol>
<li>
<p><strong>Complete freedom of choice</strong>: Select the ideal model for each specific workflow or task.</p>
</li>
<li>
<p><strong>Contextual continuity</strong>: Switch between models without losing your task context or having to repeatedly copy-paste prompts between different chatbot interfaces, dramatically improving efficiency.</p>
</li>
<li>
<p><strong>Cost-effective access</strong>: No need for multiple subscriptions to ChatGPT, Claude, Gemini, and other AI applications. With a FunBlocks AI Plan, you gain unlimited access to all premium AI models in one place.</p>
</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="using-your-own-llm-api-keys">Using Your Own LLM API Keys<a href="#using-your-own-llm-api-keys" class="hash-link" aria-label="Using Your Own LLM API Keys的直接链接" title="Using Your Own LLM API Keys的直接链接">​</a></h2>
<p>Already have API keys for OpenAI, Gemini, Claude, DeepSeek, or any OpenAI-compatible API? FunBlocks AIFlow lets you use your private API keys, allowing you to access all of FunBlocks&#x27; AI capabilities without additional subscription costs.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="how-to-set-up-your-private-llm-api-keys">How to Set Up Your Private LLM API Keys<a href="#how-to-set-up-your-private-llm-api-keys" class="hash-link" aria-label="How to Set Up Your Private LLM API Keys的直接链接" title="How to Set Up Your Private LLM API Keys的直接链接">​</a></h2>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow support using private llm API keys for free" src="/zh/assets/images/aiflow_private_api_key_setting-0a45a6b0f0ae93b2d749fcdfe486c65c.png" width="2400" height="1540" class="img_ev3q"></p>
<p>Setting up your own API keys in FunBlocks AIFlow is straightforward:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-1-access-the-model-selector">Step 1: Access the Model Selector<a href="#step-1-access-the-model-selector" class="hash-link" aria-label="Step 1: Access the Model Selector的直接链接" title="Step 1: Access the Model Selector的直接链接">​</a></h3>
<p>From the FunBlocks AIFlow editor interface, locate the model dropdown menu in the top-right corner (showing something like <code>gemini-2.0-flash</code>) and select <strong>&quot;+ Add Private LLM API Key&quot;</strong>.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-2-enter-your-api-key-information">Step 2: Enter Your API Key Information<a href="#step-2-enter-your-api-key-information" class="hash-link" aria-label="Step 2: Enter Your API Key Information的直接链接" title="Step 2: Enter Your API Key Information的直接链接">​</a></h3>
<p>In the popup window, complete the following fields:</p>
<ul>
<li>
<p><strong>Name</strong>: Create a recognizable label for this model (e.g., &quot;My GPT-4 API&quot;).</p>
</li>
<li>
<p><strong>Provider</strong>: Select your model&#x27;s service provider:</p>
<ul>
<li>OpenAI (ChatGPT)</li>
<li>Anthropic (Claude)</li>
<li>Google (Gemini)</li>
<li>DeepSeek</li>
<li>Groq</li>
<li>OpenAI Compatible API (for self-hosted models with compatible interfaces)</li>
</ul>
</li>
<li>
<p><strong>Endpoint</strong>: The default address typically populates automatically. Keep this unless you&#x27;re using a custom service.</p>
</li>
<li>
<p><strong>API Key</strong>: Paste the API key from your provider.</p>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-3-select-your-model">Step 3: Select Your Model<a href="#step-3-select-your-model" class="hash-link" aria-label="Step 3: Select Your Model的直接链接" title="Step 3: Select Your Model的直接链接">​</a></h3>
<p>Click the &quot;Choose a model&quot; dropdown menu. FunBlocks will automatically fetch available model versions from your API key (such as <code>gpt-4o</code>, <code>claude-3-opus</code>, or <code>gemini-1.5-pro</code>). Select your preferred version. You can also input modelId if not shown in the dropdown menu.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-4-confirm">Step 4: Confirm<a href="#step-4-confirm" class="hash-link" aria-label="Step 4: Confirm的直接链接" title="Step 4: Confirm的直接链接">​</a></h3>
<p>Click the <strong>&quot;CONFIRM&quot;</strong> button in the lower right to save and activate your private model.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="benefits-after-setup">Benefits After Setup<a href="#benefits-after-setup" class="hash-link" aria-label="Benefits After Setup的直接链接" title="Benefits After Setup的直接链接">​</a></h2>
<p>Once configured, you can:</p>
<ul>
<li>Seamlessly call your private models within the AIFlow whiteboard</li>
<li>Combine FunBlocks&#x27; visualization tools with your preferred AI models for brainstorming, mind mapping, and note-taking</li>
<li>Access all AI features without FunBlocks plan restrictions while using your own API keys</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="making-the-most-of-multi-model-support">Making the Most of Multi-Model Support<a href="#making-the-most-of-multi-model-support" class="hash-link" aria-label="Making the Most of Multi-Model Support的直接链接" title="Making the Most of Multi-Model Support的直接链接">​</a></h2>
<p>To maximize the benefits of FunBlocks&#x27; multi-model capabilities:</p>
<ol>
<li>
<p><strong>Experiment with different models</strong> for similar tasks to identify which performs best for your specific needs</p>
</li>
<li>
<p><strong>Use specialized models for specialized tasks</strong> – for instance, creative writing with one model and data analysis with another</p>
</li>
<li>
<p><strong>Compare outputs</strong> from different models side-by-side to gain deeper insights</p>
</li>
</ol>
<p>By thoughtfully selecting the right model for each context within FunBlocks AIFlow, you&#x27;ll experience significant improvements in both efficiency and output quality, all within a single, integrated environment.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="文件选项卡"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/docs/aiflow-tricks-and-tips/Prompts"><div class="pagination-nav__sublabel">上一页</div><div class="pagination-nav__label">Creating Custom AI Applications</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/docs/category/ai-tools"><div class="pagination-nav__sublabel">下一页</div><div class="pagination-nav__label">AI Tools</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#official-support-for-top-tier-llm-models" class="table-of-contents__link toc-highlight">Official Support for Top-Tier LLM Models</a><ul><li><a href="#why-access-to-multiple-models-matters" class="table-of-contents__link toc-highlight">Why Access to Multiple Models Matters</a></li><li><a href="#key-benefits-of-funblocks-multi-model-approach" class="table-of-contents__link toc-highlight">Key Benefits of FunBlocks&#39; Multi-Model Approach</a></li></ul></li><li><a href="#using-your-own-llm-api-keys" class="table-of-contents__link toc-highlight">Using Your Own LLM API Keys</a></li><li><a href="#how-to-set-up-your-private-llm-api-keys" class="table-of-contents__link toc-highlight">How to Set Up Your Private LLM API Keys</a><ul><li><a href="#step-1-access-the-model-selector" class="table-of-contents__link toc-highlight">Step 1: Access the Model Selector</a></li><li><a href="#step-2-enter-your-api-key-information" class="table-of-contents__link toc-highlight">Step 2: Enter Your API Key Information</a></li><li><a href="#step-3-select-your-model" class="table-of-contents__link toc-highlight">Step 3: Select Your Model</a></li><li><a href="#step-4-confirm" class="table-of-contents__link toc-highlight">Step 4: Confirm</a></li></ul></li><li><a href="#benefits-after-setup" class="table-of-contents__link toc-highlight">Benefits After Setup</a></li><li><a href="#making-the-most-of-multi-model-support" class="table-of-contents__link toc-highlight">Making the Most of Multi-Model Support</a></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>