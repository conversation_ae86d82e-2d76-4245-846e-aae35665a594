<!doctype html>
<html lang="zh" dir="ltr" class="plugin-pages plugin-id-default" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">生成式人工智能时代 | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/ai618"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docusaurus_tag" content="default"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="docsearch:docusaurus_tag" content="default"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" property="og:title" content="生成式人工智能时代 | FunBlocks AI"><meta data-rh="true" name="description" content="面向大学教师的专题讲座"><meta data-rh="true" property="og:description" content="面向大学教师的专题讲座"><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/ai618"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/ai618" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/ai618" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/ai618" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.2d3bf4a8.css">
<script src="/zh/assets/js/runtime~main.ce179126.js" defer="defer"></script>
<script src="/zh/assets/js/main.57960d62.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/ai101_neural_networks.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a class="navbar__item navbar__link" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/ai618" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/ai618" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><script type="application/ld+json">{"@context":"https://schema.org","@type":"PresentationDigitalDocument","name":"生成式人工智能时代","description":"面向大学教师的专题讲座","author":{"@type":"Organization","name":"FunBlocks AI","url":"https://funblocks.net"},"publisher":{"@type":"Organization","name":"FunBlocks AI","url":"https://funblocks.net"},"datePublished":"2024-01-01","dateModified":"2025-06-19","inLanguage":["en","zh"],"audience":{"@type":"EducationalAudience","educationalRole":"teacher"},"educationalLevel":"university","learningResourceType":"presentation","about":[{"@type":"Thing","name":"Artificial Intelligence"},{"@type":"Thing","name":"Education Technology"},{"@type":"Thing","name":"Machine Learning"},{"@type":"Thing","name":"Large Language Models"},{"@type":"Thing","name":"Generative AI"}],"teaches":["AI development history and milestones","Understanding of LLM, GenAI, and AGI concepts","Core AI learning technologies","AI training processes and methodologies","Neural network fundamentals","AI mathematical foundations","Scaling laws and emergence phenomena","AI capabilities and limitations","Human-AI collaboration strategies","AI literacy and educational applications"],"hasPart":[{"@type":"PresentationDigitalDocument","name":"AI Development History","position":1},{"@type":"PresentationDigitalDocument","name":"Why AI Now?","position":2},{"@type":"PresentationDigitalDocument","name":"Concept Clarification","position":3},{"@type":"PresentationDigitalDocument","name":"Generative AI Characteristics","position":4},{"@type":"PresentationDigitalDocument","name":"Core Learning Technologies","position":5}],"keywords":["AI education","artificial intelligence","machine learning","deep learning","large language models","generative AI","educational technology","teacher training","AI literacy","neural networks","transformer architecture","AI ethics","human-AI collaboration","educational innovation"]}</script><div class="slidesContainer_XXD4"><div class="navigation_R1ES"><button class="navButton_uZN6" disabled="">←</button><span style="margin:0 10px;font-size:12px">1<!-- --> / <!-- -->0</span><button class="navButton_uZN6">→</button></div><section class="slide_WRpt"><div class="slideContent_xNau"><h1 class="slideTitle_hD5W">人工智能与高阶思维培养：案例与操作</h1><div class="decorativeShape_aXFB circle_OtLi" style="top:10%;left:10%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:10%;right:10%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">大纲</h2><div class="outlineList_yonk"><div class="outlineItem_jW5j"><span class="outlineNumber_TVp1">01</span><span class="outlineText_Uc_l">生成式 AI 关键技术及特征</span></div><div class="outlineItem_jW5j"><span class="outlineNumber_TVp1">02</span><span class="outlineText_Uc_l">人工智能时代的人机协作</span></div><div class="outlineItem_jW5j"><span class="outlineNumber_TVp1">03</span><span class="outlineText_Uc_l">人工智能时代的教育革命</span></div><div class="outlineItem_jW5j"><span class="outlineNumber_TVp1">04</span><span class="outlineText_Uc_l">人工智能与高阶思维能力培养</span></div><div class="outlineItem_jW5j"><span class="outlineNumber_TVp1">05</span><span class="outlineText_Uc_l">案例与操作</span></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><div class="sectionDivider_F9xC"><div class="sectionNumber_dlfT">01</div><h1 class="sectionTitle_Mmsv">生成式 AI 关键技术及特征</h1><p class="sectionSubtitle_f4y1">理解核心技术与特征</p></div><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;right:15%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;left:15%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">人工智能发展简史</h2><p class="slideSubtitle_n2V8">从概念到现实的70年征程</p><div class="timeline_gi59"><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">1950</div><div class="timelineEvent_E3JN">图灵提出了图灵测试</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">1956</div><div class="timelineEvent_E3JN">达特茅斯会议，AI概念诞生</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">1980-1990s</div><div class="timelineEvent_E3JN">专家系统的兴起和AI寒冬</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">2000-2010s</div><div class="timelineEvent_E3JN">机器学习复兴</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">2012</div><div class="timelineEvent_E3JN">深度学习突破（AlexNet）</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">2017</div><div class="timelineEvent_E3JN">Transformer架构出现</div></div><div class="timelineItem_DFnv"><div class="timelineYear_JzbE">2022</div><div class="timelineEvent_E3JN">ChatGPT引发生成式AI革命</div></div></div></div></section><section class="slide_WRpt" id="slide-2"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">为什么是现在？</h2><p class="slideSubtitle_n2V8">三大要素的完美汇聚</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔧</span><h3 class="cardTitle_Yw_4">计算能力突破</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>GPU并行计算革命</li><li>云计算降低门槛</li><li>专用AI芯片的出现</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📊</span><h3 class="cardTitle_Yw_4">数据爆炸</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>互联网内容呈现爆炸式增长</li><li>数字化进程加速</li><li>成熟的数据标注技术</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🏗️</span><h3 class="cardTitle_Yw_4">技术架构</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>Transformer革命性突破</li><li>注意力机制创新</li><li>端到端学习范式</li></ul></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">端到端学习范式</h2><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">📖 <!-- -->核心概念</h3><div class="cardContent_Dn6q"><p><strong>端到端学习是一种深度学习方法，通过单一神经网络模型直接从原始输入映射到最终输出，无需人工设计中间特征提取步骤。</strong></p><p><strong>核心思想：让模型自主学习从输入到输出的最优映射关系</strong></p></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">⚡ <!-- -->主要特征</h3><ul class="bulletList_HvBV"><li><strong>直接映射：原始数据 → 深度神经网络 → 最终结果</strong></li><li><strong>整体优化：全局联合训练，避免子模块局部最优</strong></li><li><strong>自动特征学习：无需人工特征工程，模型自主学习表示</strong></li><li><strong>任务驱动：以最终目标为导向的优化策略</strong></li></ul></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🚀 <!-- -->技术优势</h3><div class="comparisonTable_bA4c"><table><thead><tr><th>优势</th><th>说明</th></tr></thead><tbody><tr><td><strong>简化架构</strong></td><td>减少人工设计，统一训练推理流程</td></tr><tr><td><strong>性能提升</strong></td><td>多领域达到SOTA，避免误差累积</td></tr><tr><td><strong>自适应性</strong></td><td>自动发现任务相关特征</td></tr><tr><td><strong>端到端优化</strong></td><td>全局最优化，梯度直接传播</td></tr></tbody></table></div></div></div></section><section class="slide_WRpt" id="slide-gpt-overview"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">GPT (Generative Pre-trained Transformers)</h2><p class="slideSubtitle_n2V8">理解现代AI的核心技术</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💡</span><h3 class="cardTitle_Yw_4">核心定义</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>生成式预训练变换器：基于Transformer架构的大型语言模型</li><li>自回归语言模型：通过预测下一个词来生成文本</li><li>无监督预训练 + 有监督微调：两阶段训练范式的典型代表</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚡</span><h3 class="cardTitle_Yw_4">关键特征</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>单向注意力机制：只能看到前面的文本，适合文本生成任务</li><li>大规模参数：从GPT-1的1.17亿到GPT-4的数千亿参数</li><li>强大的零样本和少样本学习能力</li></ul></div></div></div></div></section><section class="slide_WRpt" id="slide-gpt-capabilities"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">GPT核心能力与应用</h2><p class="slideSubtitle_n2V8">从文本生成到智能推理</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">✍️</span><h3 class="cardTitle_Yw_4">文本生成能力</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>创意写作：故事、诗歌、剧本创作</li><li>技术文档：API文档、用户手册、技术报告</li><li>营销内容：广告文案、产品描述、社交媒体内容</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤔</span><h3 class="cardTitle_Yw_4">理解与推理</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>阅读理解：回答基于文本的复杂问题</li><li>逻辑推理：解决数学问题和逻辑谜题</li><li>知识问答：跨领域的百科知识查询</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💻</span><h3 class="cardTitle_Yw_4">代码生成</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>程序编写：根据需求生成代码</li><li>代码解释：理解和注释现有代码</li><li>调试辅助：发现和修复代码错误</li></ul></div></div></div></div></section><section class="slide_WRpt" id="slide-gpt-limitations"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">GPT技术优势与局限</h2><p class="slideSubtitle_n2V8">理性认识GPT的能力边界</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">✅</span><h3 class="cardTitle_Yw_4">主要优势</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>泛化能力强：一个模型处理多种任务</li><li>上下文学习：通过示例快速适应新任务</li><li>创造性输出：生成新颖且有用的内容</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚠️</span><h3 class="cardTitle_Yw_4">当前局限</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>幻觉问题：可能生成看似合理但实际错误的信息</li><li>知识截止：训练数据存在时间限制</li><li>计算成本高：推理需要大量计算资源</li><li>可解释性差：难以理解模型的决策过程</li></ul></div></div></div></div></section><section class="slide_WRpt" id="slide-4"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">生成式人工智能的特点</h2><p class="slideSubtitle_n2V8">从识别到创造的跨越</p><div class="contentSection_AeJB"><div class="cardContent_Dn6q"><h3 class="cardTitle_Yw_4">🆚 Differences from Traditional AI</h3><div class="comparisonTable_bA4c"><table><thead><tr><th>传统AI</th><th>生成式AI</th></tr></thead><tbody><tr><td>识别和分类</td><td>内容创作</td></tr><tr><td>规则驱动</td><td>数据驱动</td></tr><tr><td>专用系统</td><td>通用能力</td></tr><tr><td>确定性输出</td><td>概率生成</td></tr></tbody></table></div></div><div><h3 class="cardTitle_Yw_4">🔗 <!-- -->核心相关技术</h3><ul class="bulletList_HvBV"><li>深度神经网络</li><li>注意力机制</li><li>预训练与微调范式</li><li>强化学习对齐</li></ul></div></div></div></section><section class="slide_WRpt" id="slide-7"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">神经网络：模仿大脑的智能</h2><p class="slideSubtitle_n2V8">从生物启发到人工实现</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">生物神经元</h3><div class="cardContent_Dn6q">生物神经元传递电信号</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤖</span><h3 class="cardTitle_Yw_4">人工神经元</h3><div class="cardContent_Dn6q">人工神经元处理数值</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🏗️</span><h3 class="cardTitle_Yw_4">深度网络</h3><div class="cardContent_Dn6q">多层结构实现复杂模式识别</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🔗 <!-- -->网络架构</h3><div class="neuralNetworkContainer_teC3"><div class="neuralNetworkImage_GTDI"><img class="featureImage_SBFb" alt="Neural Networks Architecture" src="/img/portfolio/fullsize/ai101_neural_networks.png"></div><div class="neuralNetworkContent_wyul"><div class="codeBlock_oNs7">输入层 → 隐藏层 → 输出层
    ↓        ↓        ↓
  原始数据 → 特征提取 → 预测结果</div></div></div></div></div></section><section class="slide_WRpt" id="slide-concepts-explained"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">参数、维度、Token概念解析</h2><p class="slideSubtitle_n2V8">理解大语言模型的核心概念</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧩</span><h3 class="cardTitle_Yw_4">Token（词元）</h3><div class="cardContent_Dn6q"><p>定义：token是模型处理文本的最小单位，可以是单个字、词，甚至是部分词。由分词器（Tokenizer）将原始文本切分而成。</p><div class="codeBlock_oNs7">例子：
英文中：&quot;ChatGPT is great&quot; 可能被分成：[&quot;Chat&quot;, &quot;G&quot;, &quot;PT&quot;, &quot; is&quot;, &quot; great&quot;]
中文中：&quot;大模型很好用&quot; 可能被分成：[&quot;大&quot;, &quot;模型&quot;, &quot;很&quot;, &quot;好&quot;, &quot;用&quot;]</div><p class="analogy_BIv4">⌨️ <!-- -->类比：如果你把一句话看作一段积木搭的墙，tokens就是每一块积木。</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📦</span><h3 class="cardTitle_Yw_4">维（维度 / Dimensions）</h3><div class="cardContent_Dn6q"><p>定义：维是向量或矩阵中每个数据点的位置或&quot;长度&quot;。常用于描述隐藏层中的向量空间大小。</p><div class="codeBlock_oNs7">常见使用：
词向量的维度（embedding size）：比如一个单词被映射为一个768维的向量。
隐藏层维度（hidden size）：表示每层中每个神经元输出的向量长度。</div><p class="analogy_BIv4">📦 <!-- -->类比：如果一个token是一个商品，维度就是它的&quot;特征标签&quot;数量，比如颜色、大小、用途等。</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">参数（Parameters）</h3><div class="cardContent_Dn6q"><p>定义：参数是模型在训练中学到的&quot;知识&quot;。它们包括神经网络中的权重（weights）和偏置（biases）。</p><p>数量级：GPT-3有1750亿个参数，GPT-4据推测参数更多。</p><p class="analogy_BIv4">🧠 <!-- -->类比：把模型比作一个大脑，参数就是它大脑中形成的&quot;记忆连接&quot;或&quot;经验&quot;。</p></div></div></div></div></section><section class="slide_WRpt" id="slide-6"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">AI训练的三个关键阶段</h2><p class="slideSubtitle_n2V8">从原始到智能的蜕变</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📚</span><h3 class="cardTitle_Yw_4">阶段1：预训练</h3><div class="cardContent_Dn6q">预训练：从海量文本数据中学习语言模式</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">阶段2：监督微调</h3><div class="cardContent_Dn6q">监督微调：学习遵循指令</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🏆</span><h3 class="cardTitle_Yw_4">阶段3：强化学习</h3><div class="cardContent_Dn6q">强化学习：与人类偏好对齐</div></div></div><div class="contentSection_AeJB"><div class="codeBlock_oNs7">原始文本 → 语言模型 → 指令跟随者 → 人类对齐AI</div></div></div></section><section class="slide_WRpt" id="slide-8"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">AI的数学基础</h2><p class="slideSubtitle_n2V8">概率世界中的智能涌现</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎲</span><h3 class="cardTitle_Yw_4">概率论</h3><div class="cardContent_Dn6q">一切皆概率 - 没有绝对确定性</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📊</span><h3 class="cardTitle_Yw_4">统计学</h3><div class="cardContent_Dn6q">通过统计方法从数据中学习模式</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚡</span><h3 class="cardTitle_Yw_4">优化理论</h3><div class="cardContent_Dn6q">持续优化寻找最佳解决方案</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🧮 <!-- -->核心数学概念</h3><ul class="bulletList_HvBV"><li>线性代数：向量空间和变换</li><li>微积分：梯度下降和反向传播</li><li>信息论：熵和压缩</li><li>图论：网络结构和关系</li></ul></div></div></section><section class="slide_WRpt" id="slide-11"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">Scaling Law：规模的魔力</h2><p class="slideSubtitle_n2V8">更大就是更强？</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📈</span><h3 class="cardTitle_Yw_4">更多参数</h3><div class="cardContent_Dn6q">更多参数 → 更好性能</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💾</span><h3 class="cardTitle_Yw_4">更多数据</h3><div class="cardContent_Dn6q">更多数据 → 更多知识</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚡</span><h3 class="cardTitle_Yw_4">更多计算</h3><div class="cardContent_Dn6q">更多计算 → 更好训练</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">📊 <!-- -->规模化趋势</h3><ul class="bulletList_HvBV"><li>性能随规模可预测地提升</li><li>在特定阈值出现涌现能力</li><li>但规模化有物理和经济限制</li><li>效率改进变得至关重要</li></ul></div></div></section><section class="slide_WRpt" id="slide-12"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">AI真的&quot;懂&quot;吗？</h2><p class="slideSubtitle_n2V8">统计模式 vs 真正理解</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📊</span><h3 class="cardTitle_Yw_4">统计掌握</h3><div class="cardContent_Dn6q">AI擅长统计模式匹配</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤔</span><h3 class="cardTitle_Yw_4">语义理解？</h3><div class="cardContent_Dn6q">但它真的理解含义吗？</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🎭 <!-- -->中文房间论证</h3><div class="codeBlock_oNs7">房间里的人按规则回应中文字符
↓
看起来理解中文，但实际上不懂
↓
类似地，AI可能模拟理解而非真正理解</div><h3 class="cardTitle_Yw_4">🔬 <!-- -->当前证据</h3><ul class="bulletList_HvBV"><li>AI展现出卓越的语言能力</li><li>能够推理抽象概念</li><li>但缺乏真实世界的基础经验</li><li>理解vs复杂模式匹配仍有争议</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">LLM 幻觉</h2><p class="slideSubtitle_n2V8">什么是幻觉？</p><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">定义</h3><div class="cardContent_Dn6q">模型生成看似合理但实际上不准确或不存在的信息</div><h3 class="cardTitle_Yw_4">主要幻觉类型</h3><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔍</span><h4 class="cardTitle_Yw_4">事实幻觉</h4><ul class="bulletList_HvBV"><li>虚假信息：生成不存在的历史事件、人物或数据</li><li>假引文：捏造不存在的学术论文、网站链接</li><li>数值错误：提供不正确的统计数据、日期、数量</li></ul></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h4 class="cardTitle_Yw_4">逻辑幻觉</h4><ul class="bulletList_HvBV"><li>推理错误：逻辑推理中的谬误</li><li>因果混淆：错误地确立因果关系</li><li>自相矛盾：同一响应中的矛盾性陈述</li></ul></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎭</span><h4 class="cardTitle_Yw_4">创造性幻觉</h4><ul class="bulletList_HvBV"><li>虚构内容：创造不存在的故事、人物、作品</li><li>混合信息：错误地结合来自不同来源的信息</li></ul></div></div><h3 class="cardTitle_Yw_4">原因</h3><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><h4 class="cardTitle_Yw_4">训练数据问题</h4><ul class="bulletList_HvBV"><li>训练数据中的错误</li><li>不完整的训练覆盖</li><li>过时或相互矛盾的信息</li></ul></div><div class="contentCard_HTL6"><h4 class="cardTitle_Yw_4">模型机制限制</h4><ul class="bulletList_HvBV"><li>基于概率的生成</li><li>缺乏现实世界知识验证</li><li>上下文理解限制</li></ul></div></div><h3 class="cardTitle_Yw_4">识别和预防策略</h3><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><h4 class="cardTitle_Yw_4">用户级</h4><ul class="bulletList_HvBV"><li>交叉验证：从多个来源验证重要信息</li><li>批判性思维：特别是对于特定数据，保持怀疑</li><li>专业判断：在专业领域依靠权威资源</li></ul></div><div class="contentCard_HTL6"><h4 class="cardTitle_Yw_4">技术级</h4><ul class="bulletList_HvBV"><li>检索增强生成（RAG）：与实时知识库结合</li><li>多模型验证：使用多个模型进行交叉验证</li><li>置信度评估：标记答案可靠性</li></ul></div></div><h3 class="cardTitle_Yw_4">要点</h3><blockquote>🚨 记住：大型语言模型是强大的工具，但需要人类判断和验证以确保信息准确性</blockquote></div></div></section><section class="slide_WRpt" id="slide-13"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">能否超越人类？</h2><p class="slideSubtitle_n2V8">迈向通用人工智能的征程</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">当前状态</h3><div class="cardContent_Dn6q">当前AI：狭窄的专门能力</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🚀</span><h3 class="cardTitle_Yw_4">AGI愿景</h3><div class="cardContent_Dn6q">AGI目标：跨所有领域的通用智能</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🏆 <!-- -->AI vs 人类能力</h3><div class="comparisonTable_bA4c"><table><thead><tr><th>领域</th><th>AI状态</th><th>人类水平</th></tr></thead><tbody><tr><td>国际象棋/围棋</td><td>✅ 超人类</td><td>已超越</td></tr><tr><td>图像识别</td><td>✅ 人类水平</td><td>已匹配</td></tr><tr><td>语言任务</td><td>🔄 接近中</td><td>接近人类</td></tr><tr><td>通用推理</td><td>❓ 不确定</td><td>低于人类</td></tr><tr><td>创造力</td><td>🎨 新兴中</td><td>有争议</td></tr></tbody></table></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><div class="sectionDivider_F9xC"><div class="sectionNumber_dlfT">02</div><h1 class="sectionTitle_Mmsv">人工智能时代的人机协作</h1></div><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;right:15%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;left:15%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">新能力：AI增强人类成就</h2><p class="slideSubtitle_n2V8">增强引擎：拓展人类成就的新疆域</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔬</span><h3 class="cardTitle_Yw_4">颠覆科学发现：从蛋白质到新材料</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>AlphaFold：解决蛋白质折叠问题，预测超2亿种蛋白质结构，为全球节省约10亿年研究时间</li><li>MatterGen：&quot;逆向设计&quot;新材料，AI在1小时内提出12万个候选结构</li><li>科学家角色转变：从&quot;实验者&quot;到&quot;假说策划者&quot;与&quot;探究设计师&quot;</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎨</span><h3 class="cardTitle_Yw_4">催生新文艺复兴：艺术、音乐与设计的伙伴</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>AI音乐 (MuseNet)：辅助创作、编曲，根据文本生成特定情绪音乐</li><li>AI绘画 (Midjourney)：降低视觉表达门槛，成为艺术家的&quot;创意加速器&quot;</li><li>创作者价值转移：从&quot;技术执行&quot;到&quot;概念策划&quot;与&quot;审美判断&quot;</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">个性化世界：从精准医疗到定制化体验</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>精准医疗：基于个人基因、影像数据，提供定制化治疗方案</li><li>定制化消费：电商、流媒体为每个用户打造独一无二的推荐与体验</li><li>自适应学习：根据学生进度动态调整教学内容，实现&quot;因材施教&quot;</li></ul></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">大变革：AI重塑工作与生活</h2><p class="slideSubtitle_n2V8">范式转移：从&quot;岗位替代&quot;到&quot;任务重构&quot;</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🌍</span><h3 class="cardTitle_Yw_4">全球劳动力市场剧变</h3><div class="cardContent_Dn6q"><p>核心转变：从&quot;职业&quot;到&quot;任务&quot; - AI自动化的是工作中的具体&quot;任务&quot;（约占30%-70%），而非整个&quot;职业&quot;。</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔄</span><h3 class="cardTitle_Yw_4">AI+X复合型人才</h3><div class="cardContent_Dn6q"><p>催生了&quot;AI+X&quot;复合型人才的需求，强调终身学习以维持职业韧性。</p></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">法律行业案例</h3><div class="codeBlock_oNs7">AI自动化：法律研究、文件审阅、合同起草
↓
律师专注：复杂诉讼策略、客户共情、伦理推理、创造性解决方案
↓
结果：职业发展路径从线性阶梯变为动态&quot;技能晶格&quot;</div></div></div></section><section class="slide_WRpt" id="slide-15"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">AI时代的生存之道</h2><p class="slideSubtitle_n2V8">适应变化，拥抱未来</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔄</span><h3 class="cardTitle_Yw_4">持续学习</h3><div class="cardContent_Dn6q">拥抱变化和持续学习</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤝</span><h3 class="cardTitle_Yw_4">AI协作</h3><div class="cardContent_Dn6q">学会与AI作为伙伴协作</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💡</span><h3 class="cardTitle_Yw_4">人类独特性</h3><div class="cardContent_Dn6q">专注于独特的人类能力</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🎯 <!-- -->战略方法</h3><div class="codeBlock_oNs7">短期：学习AI工具和工作流程
中期：发展人机协作技能
长期：专注于创造力、同理心和复杂推理</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">人类核心素养：后自动化世界的价值锚点</h2><p class="slideSubtitle_n2V8">价值转向：从&quot;做什么&quot;转向&quot;如何思考与协作&quot;</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">高阶认知能力</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>分析性思维、创造性思维</li><li>元认知技能和学习策略</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤝</span><h3 class="cardTitle_Yw_4">社会情感能力</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>领导力、协作力、沟通能力</li><li>共情、说服和激励他人的能力</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💪</span><h3 class="cardTitle_Yw_4">个人特质</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>韧性、灵活性、敏捷性、自我驱动</li><li>AI与大数据素养</li></ul></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">核心隐喻：AI作为&quot;认知外骨骼&quot;</h3><div class="codeBlock_oNs7">关系重构：从&quot;人机对立&quot;转向&quot;人机共生&quot;
↓
目标：将人类从重复性认知劳动中解放，专注于更高阶的创造与战略思考</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">培养AI素养</h2><p class="slideSubtitle_n2V8">技术应该放大人类潜能，而非取代人类</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💡</span><h3 class="cardTitle_Yw_4">理解AI</h3><div class="cardContent_Dn6q">掌握AI的能力、局限性和应用</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚖️</span><h3 class="cardTitle_Yw_4">伦理意识</h3><div class="cardContent_Dn6q">理解伦理影响和责任</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🛠️</span><h3 class="cardTitle_Yw_4">实践技能</h3><div class="cardContent_Dn6q">发展AI交互的实际能力</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">AI素养课程</h3><ul class="bulletList_HvBV"><li>AI基础知识和概念</li><li>伦理考虑和影响</li><li>实用AI工具和应用</li><li>批判性思维和评估</li><li>未来趋势和发展</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">人-AI协作模型</h2><p class="slideSubtitle_n2V8">互补认知优势实现最佳结果</p><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🤝 <!-- -->互补优势</h3><div class="comparisonTable_bA4c"><table><thead><tr><th>人类优势</th><th>AI优势</th></tr></thead><tbody><tr><td>新颖和创意想法</td><td>实用、可实施的解决方案</td></tr><tr><td>上下文理解</td><td>大规模数据处理</td></tr><tr><td>伦理推理</td><td>模式识别</td></tr><tr><td>情商</td><td>一致性表现</td></tr></tbody></table></div></div><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎨</span><h3 class="cardTitle_Yw_4">AI作为创意伙伴</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>生成多样化的想法和观点</li><li>挑战假设和偏见</li><li>提供替代观点</li><li>支持构思和头脑风暴</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">元认知脚手架</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>学习模式分析</li><li>认知过程洞察</li><li>反思支持和指导</li><li>策略推荐</li></ul></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">⚖️ <!-- -->认知劳动的最优分工</h3><div class="codeBlock_oNs7">AI处理：常规处理、数据分析、初始内容生成
              
人类专注：批判性评估、创意综合、伦理推理、最终决策

结果：通过互补协作增强认知结果</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><div class="sectionDivider_F9xC"><div class="sectionNumber_dlfT">03</div><h1 class="sectionTitle_Mmsv">人工智能时代的教育革命</h1></div><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;right:15%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;left:15%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">再思考：AI时代的教育革命</h2><p class="slideSubtitle_n2V8">教育的文艺复兴：目标、内容与方法的全面重塑</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🌟</span><h3 class="cardTitle_Yw_4">新的北极星：重新定义教育目的</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>从&quot;知识传授&quot;到&quot;素养培育&quot;：当知识唾手可得，教育回归&quot;立德树人&quot;的本质</li><li>从&quot;知道&quot;到&quot;成为&quot;：目标是培养能自主学习、解决复杂问题、做出伦理判断的&quot;终身学习者&quot;</li><li>核心素养框架：各国均提出整合&quot;知识、思维、价值、实践&quot;的&quot;四位一体&quot;素养模型</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔧</span><h3 class="cardTitle_Yw_4">解构的课堂：课程与教学法</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>教学法转向：以项目式学习（PBL）、探究式学习为核心</li><li>课程重构：从学科本位转向素养本位，强调跨学科整合和真实问题解决</li><li>学习空间变革：从固定教室转向灵活学习环境，支持个性化和协作学习</li></ul></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">人工智能时代更广泛的教育变革</h2><p class="slideSubtitle_n2V8">涵盖课程、教学法、师生角色、评估方式的系统性变革</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📚</span><h3 class="cardTitle_Yw_4">课程与教学法的重新定义</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>向能力本位与跨学科学习的转变</li><li>强调&quot;机器无法替代的技能&quot;</li><li>AI素养的整合作为核心课程</li><li>个性化与自适应课程</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">👥</span><h3 class="cardTitle_Yw_4">教育者与学习者角色的演进</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>教育者成为&quot;学习架构师&quot;与&quot;向导&quot;</li><li>学习者成为主动的共同创造者与批判性消费者</li><li>新技能需求：AI素养、数据分析、伦理指导</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📊</span><h3 class="cardTitle_Yw_4">评估方法的新路径</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>AI驱动的形成性与总结性评估</li><li>个性化与自适应评估</li><li>聚焦真实性、过程性与持续性反馈</li><li>对传统评估有效性的挑战</li></ul></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">以人为本的AI教育</h3><div class="codeBlock_oNs7">系统性教育范式转变：

课程 → 能力本位，AI素养整合
角色 → 教育者为架构师，学习者为共创者
评估 → 真实性、持续性、人机协作
目标 → 在AI世界中培养人类福祉、能动性和伦理发展</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">新兴教育理念的曙光</h2><p class="slideSubtitle_n2V8">人与AI共生学习的新范式</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤝</span><h3 class="cardTitle_Yw_4">人-AI协同学习</h3><div class="cardContent_Dn6q"><p>人类与AI系统在协作中共同学习、相互适应并随时间共同进化</p><ul class="bulletList_HvBV"><li>共享心智模型和共同基础</li><li>AI作为团队伙伴，而非仅仅是工具</li><li>通过互动实现相互学习和适应</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">以人为本的AI优先教育 (HCAIF)</h3><div class="cardContent_Dn6q"><p>在充分利用AI能力的同时，优先考虑人类价值观、伦理应用和个性化反馈</p><ul class="bulletList_HvBV"><li>AI增强而非取代人类能力</li><li>整合技术、用户体验和伦理考量</li><li>强调AI使用的明确说明和反思</li></ul></div></div></div><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">&quot;机器无法替代的技能&quot;哲学立场</h3><div class="cardContent_Dn6q"><p>专注于发展AI无法复制的独特人类能力</p><ul class="bulletList_HvBV"><li>批判性思维、创造力、情感智能</li><li>伦理判断和复杂协作能力</li><li>以人为中心的创新和价值驱动的成果</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔍</span><h3 class="cardTitle_Yw_4">面向&quot;认识论知识&quot;与适应性</h3><div class="cardContent_Dn6q"><p>理解知识是如何构建、验证和应用于快速变化的世界</p><ul class="bulletList_HvBV"><li>元认知技能和学习策略</li><li>适应性和终身学习能力</li><li>整合编程、数据科学、复杂系统</li></ul></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">终身学习的新范式</h2><p class="slideSubtitle_n2V8">从阶段性教育到持续成长</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔄</span><h3 class="cardTitle_Yw_4">持续学习</h3><div class="cardContent_Dn6q">将学习作为贯穿一生的持续过程</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">即时学习</h3><div class="cardContent_Dn6q">在需要时和需要的地方学习</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">👥</span><h3 class="cardTitle_Yw_4">协作学习</h3><div class="cardContent_Dn6q">通过人机协作进行学习</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">构建学习基础设施</h3><div class="codeBlock_oNs7">提升学习力，发展元学习能力</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">新工具：AI驱动的教育产品生态</h2><p class="slideSubtitle_n2V8">从&quot;内容分发器&quot;到&quot;认知合作伙伴&quot;</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎯</span><h3 class="cardTitle_Yw_4">智能辅导与自适应学习平台</h3><div class="cardContent_Dn6q"><p>实现大规模&quot;因材施教&quot;</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤖</span><h3 class="cardTitle_Yw_4">教师与管理者&quot;副驾驶&quot;</h3><div class="cardContent_Dn6q"><p>AI辅助备课、出题、排课，为教师减负增效</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🥽</span><h3 class="cardTitle_Yw_4">沉浸式与体验式学习环境</h3><div class="cardContent_Dn6q"><p>VR/AR虚拟实验、历史场景重现，提升学习趣味性</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">📄</span><h3 class="cardTitle_Yw_4">内容交互与分析工具</h3><div class="cardContent_Dn6q"><p>与PDF文档对话、总结要点、整理笔记</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">👥</span><h3 class="cardTitle_Yw_4">协同学习支持平台</h3><div class="cardContent_Dn6q"><p>AI辅助智能分组、引导讨论，增强团队协作</p></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">&quot;认知健身房&quot;：主动促进批判性与创造力</h3><div class="cardContent_Dn6q"><p>制造并管理&quot;有益的认知摩擦&quot;，而非一味追求便利，以防&quot;认知外包&quot;</p></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><div class="sectionDivider_F9xC"><div class="sectionNumber_dlfT">04</div><h1 class="sectionTitle_Mmsv">人工智能与高阶思维能力培养</h1></div><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;right:15%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;left:15%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">AI时代的高阶思维</h2><p class="slideSubtitle_n2V8">从信息回忆到深度智力参与</p><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🧠 <!-- -->什么是高阶思维？</h3><div class="cardContent_Dn6q"><p>高阶思维技能包含复杂的认知过程，区别于单纯的信息回忆，涉及深度智力参与、创造力和批判性分析。这些包括分析、评估和创造——需要批判性检查信息、基于标准做出判断，并将知识综合成新颖配置的认知操作。</p></div></div><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔍</span><h3 class="cardTitle_Yw_4">分析</h3><div class="cardContent_Dn6q">将复杂信息分解为组成部分并理解关系</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚖️</span><h3 class="cardTitle_Yw_4">评估</h3><div class="cardContent_Dn6q">基于标准和准则做出判断，评估质量和有效性</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎨</span><h3 class="cardTitle_Yw_4">创造</h3><div class="cardContent_Dn6q">将知识综合成新颖配置并产生原创想法</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🤖 <!-- -->AI时代的重新概念化</h3><ul class="bulletList_HvBV"><li>批判性评估AI生成内容的能力</li><li>理解算法局限性和偏见</li><li>在技术中介环境中保持人类主体性</li><li>对人-AI认知互动的元认知意识</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">认知卸载现象</h2><p class="slideSubtitle_n2V8">AI如何改变人类思维过程</p><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">📚 <!-- -->什么是认知卸载？</h3><div class="cardContent_Dn6q"><p>认知卸载代表使用外部工具或资源来减少心理努力并增强认知表现的实践。这种现象包括将记忆存储、计算以及越来越多的复杂推理过程委托给AI系统。</p></div></div><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🧠</span><h3 class="cardTitle_Yw_4">传统卸载</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>笔记和外部记忆</li><li>计算器进行计算</li><li>地图导航</li><li>保留高阶思维</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤖</span><h3 class="cardTitle_Yw_4">AI时代卸载</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>复杂分析和综合</li><li>创意内容生成</li><li>决策支持</li><li>可能影响技能发展</li></ul></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">⚠️ <!-- -->对学习的影响</h3><ul class="bulletList_HvBV"><li>减少持续分析参与机会的风险</li><li>对执行功能发展的潜在影响</li><li>关于智力自主性和适应性的问题</li><li>需要平衡的人-AI认知伙伴关系</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">防风险：驾驭双刃剑</h2><p class="slideSubtitle_n2V8">规避认知外包与构建共治模型</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⚠️</span><h3 class="cardTitle_Yw_4">认知外包风险</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>悖论：AI既是&quot;认知健身房&quot;，也可能是&quot;认知拐杖&quot;</li><li>关键变量：影响好坏的不是AI工具本身，而是其所处的&quot;教学法情境&quot;</li><li>应对之道：对教师进行系统性培训，使其掌握能够利用AI促进高阶思维的教学设计能力</li></ul></div></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">负责任AI整合框架</h2><p class="slideSubtitle_n2V8">保持高阶思维的系统性方法</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🏛️</span><h3 class="cardTitle_Yw_4">UNESCO AI能力框架</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li>以人为中心的思维发展</li><li>AI伦理理解</li><li>AI技术和应用掌握</li><li>AI系统设计参与</li></ul></div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎓</span><h3 class="cardTitle_Yw_4">SchoolAI 4C框架</h3><div class="cardContent_Dn6q"><ul class="bulletList_HvBV"><li><strong>Conscientious:</strong> <!-- -->理解AI能力和局限性</li><li><strong>Collaborative:</strong> <!-- -->将AI用作学习伙伴</li><li><strong>Critical:</strong> <!-- -->批判性评估AI输出</li><li><strong>Creative:</strong> <!-- -->利用AI进行创意目的</li></ul></div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">🔄 <!-- -->FunBlocks AI框架</h3><div class="cardContent_Dn6q"><p>与替代人类认知的传统人-AI交互设计不同，FunBlocks AI通过提出问题或提供替代观点而非直接答案来促进认知参与。这种方法确保学生在受益于AI能力的同时保持积极的分析思维参与。</p></div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><div class="sectionDivider_F9xC"><div class="sectionNumber_dlfT">05</div><h1 class="sectionTitle_Mmsv">案例与操作</h1></div><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;right:15%"></div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;left:15%"></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">FunBlocks AI</h2><p class="slideSubtitle_n2V8">与AI共同探索、思考与创造</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔍</span><h3 class="cardTitle_Yw_4">分析和批判性思维</h3><div class="cardContent_Dn6q">细化问题、分析谬误、发展批判性思维</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💡</span><h3 class="cardTitle_Yw_4">创新思维</h3><div class="cardContent_Dn6q">与AI一起生成和发展创意想法</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⭐</span><h3 class="cardTitle_Yw_4">无限探索</h3><div class="cardContent_Dn6q">使用AI在无限画布上从多个视角探索无限可能</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">⭐</span><h3 class="cardTitle_Yw_4">AI增强的思维</h3><div class="cardContent_Dn6q">AI生成思维导图和头脑风暴</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤖</span><h3 class="cardTitle_Yw_4">AI驱动的经典思维模型应用</h3><div class="cardContent_Dn6q">使用AI辅助应用经典心理模型解决复杂问题</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎨</span><h3 class="cardTitle_Yw_4">创意工作流</h3><div class="cardContent_Dn6q">从概念到演示的集成AI工具</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">特色功能</h3><ul class="bulletList_HvBV"><li>AI 思维导图和头脑风暴</li><li>AI 协作思考空间</li><li>与主流 AI 模型集成</li><li>AI 驱动的批判性分析</li><li>AI 提出问题或提供不同视角</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">突破线性思维的局限</h2><p class="slideSubtitle_n2V8">从对话线到无限画布</p><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">线性对话 vs 多视角探索</h3><div class="comparisonTable_bA4c"><table><thead><tr><th>线性对话</th><th>多视角探索</th></tr></thead><tbody><tr><td>单向对话</td><td>多向探索</td></tr><tr><td>单一视角</td><td>多重视角</td></tr><tr><td>眼界越来越狭窄</td><td>眼界越来越广阔</td></tr><tr><td>适用于快速获得答案</td><td>适用于深入思考和探索</td></tr><tr><td>专注于结果</td><td>专注于过程</td></tr></tbody></table></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">多视角思维优势</h3><ul class="bulletList_HvBV"><li>通过多个视角和连接增强创造力</li><li>通过多个视角获得更好的问题解决能力</li><li>通过批判性思维和可视化改进学习和记忆</li><li>通过多个视角获得更全面的理解</li><li>支持复杂问题分解，分而治之</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">用AI提升思考能力</h2><p class="slideSubtitle_n2V8">让AI辅助思考，但不代替思考</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤝</span><h3 class="cardTitle_Yw_4">AI作为伙伴</h3><div class="cardContent_Dn6q">与AI协作扩展思维能力</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🔍</span><h3 class="cardTitle_Yw_4">增强分析</h3><div class="cardContent_Dn6q">处理和分析复杂信息模式</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">💡</span><h3 class="cardTitle_Yw_4">创意催化剂</h3><div class="cardContent_Dn6q">生成和探索新想法和可能性</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">思维增强策略</h3><ul class="bulletList_HvBV"><li>使用AI进行初步想法生成</li><li>应用人类判断进行优化</li><li>结合多个视角</li><li>持续迭代和改进</li><li>保持批判性思维</li></ul><div class="codeBlock_oNs7">结合人类批判性思维和AI处理能力以增强决策</div></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h2 class="slideTitle_hD5W">总结与展望</h2><p class="slideSubtitle_n2V8">拥抱AI时代的工作和终身学习变革</p><div class="contentGrid_t1Be"><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🎓</span><h3 class="cardTitle_Yw_4">教育转型</h3><div class="cardContent_Dn6q">适应新的学习范式和机遇</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🤝</span><h3 class="cardTitle_Yw_4">人机协作</h3><div class="cardContent_Dn6q">利用人类和人工智能的优势</div></div><div class="contentCard_HTL6"><span class="cardIcon_WzjF">🌟</span><h3 class="cardTitle_Yw_4">持续适应</h3><div class="cardContent_Dn6q">紧跟AI能力和应用的发展</div></div></div><div class="contentSection_AeJB"><h3 class="cardTitle_Yw_4">关键要点</h3><ul class="bulletList_HvBV"><li>AI是增强人类能力的工具</li><li>专注于发展独特的人类技能</li><li>拥抱持续学习和适应</li><li>保持伦理意识和责任感</li><li>建立有效的人机协作</li><li>用AI塑造教育的未来</li></ul></div></div></section><section class="slide_WRpt"><div class="slideContent_xNau"><h1 class="slideTitle_hD5W">AI已来，拥抱并塑造未来!</h1><div class="decorativeShape_aXFB circle_OtLi" style="top:20%;left:15%">https://funblocks.net</div><div class="decorativeShape_aXFB triangle_hPjv" style="bottom:20%;right:15%"></div></div></section></div></div></div>
</body>
</html>