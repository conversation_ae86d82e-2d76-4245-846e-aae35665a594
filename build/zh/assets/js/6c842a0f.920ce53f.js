"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[86823],{21099:(e,i,t)=>{t.d(i,{A:()=>s});const s={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},26167:(e,i,t)=>{t.d(i,{A:()=>c});const s="footer_m3PR",r="footerContainer_g8s3",n="footerLinks_EjWI",o="toolsGrid_N_gp",a="copyright_zlJy";var l=t(74848);const c=function(){return(0,l.jsx)("footer",{className:s,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:r,children:[(0,l.jsxs)("div",{className:n,style:{marginRight:"20px"},children:[(0,l.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,l.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,l.jsxs)("div",{className:n,children:[(0,l.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/welcome_extension",children:"Chrome Extension: FunBlocks AI Assistant"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/prompt_optimizer",children:"Chrome Extension: AI Prompt Optimizer"})})]})]}),(0,l.jsxs)("div",{className:n,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,l.jsxs)("div",{className:n,children:[(0,l.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,l.jsx)("ul",{children:(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,l.jsx)("div",{className:r,children:(0,l.jsxs)("div",{className:n,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,l.jsx)("div",{className:a,children:(0,l.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},32427:(e,i,t)=>{t.d(i,{A:()=>u});t(96540);var s=t(50539),r=t(9303);const n="benefitsSection_jOdI",o="sectionTitle_t9_h",a="sectionDescription_MTcl",l="benefitsGrid_UuKA",c="benefitCard_v8OV",d="benefitIcon_gytJ",p="benefitTitle_XlcY",m="benefitDescription_bJfg";var h=t(74848);const u=function(e){let{page:i="extension_welcome",customBenefits:t=null,backgroundColor:u="honeydew"}=e;const f=t||[{icon:"\ud83d\udd04",titleId:`${i}.benefits.benefit4.title`,title:"Seamless Integration",descriptionId:`${i}.benefits.benefit4.description`,description:"Works across all websites with context-aware tools that understand what you're doing and provide relevant assistance."},{icon:"\ud83d\ude80",titleId:`${i}.benefits.benefit1.title`,title:"Enhanced Productivity",descriptionId:`${i}.benefits.benefit1.description`,description:"Save time and work more efficiently with AI-powered tools that streamline your reading, writing, and thinking processes."},{icon:"\u26a1",titleId:`${i}.benefits.benefit2.title`,title:"Improved Critical Thinking",descriptionId:`${i}.benefits.benefit2.description`,description:"Develop stronger analytical skills with structured thinking frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking."},{icon:"\ud83d\udca1",titleId:`${i}.benefits.benefit3.title`,title:"Boosted Creativity",descriptionId:`${i}.benefits.benefit3.description`,description:"Unlock new ideas and perspectives with AI-powered brainstorming and mindmapping tools that expand your creative horizons."},{icon:"\ud83d\udcca",titleId:`${i}.benefits.benefit5.title`,title:"Visual Learning",descriptionId:`${i}.benefits.benefit5.description`,description:"Transform complex information into visual formats like mindmaps, infographics, and insight cards for better understanding and retention."},{icon:"\ud83d\udd12",titleId:`${i}.benefits.benefit6.title`,title:"Privacy & Control",descriptionId:`${i}.benefits.benefit6.description`,description:"Select your preferred AI models within a unified workflow, maximizing productivity without incurring extra costs."}];return(0,h.jsx)("section",{id:"benefits",className:n,style:{backgroundColor:u},children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(r.A,{as:"h2",className:o,children:(0,h.jsx)(s.A,{id:`${i}.benefits.title`,children:"Key Benefits"})}),(0,h.jsx)("p",{className:a,children:(0,h.jsx)(s.A,{id:`${i}.benefits.description`,children:"Discover how FunBlocks AI MindMap Extension transforms your browsing experience with these powerful benefits:"})}),(0,h.jsx)("div",{className:l,children:f.map(((e,i)=>(0,h.jsxs)("div",{className:c,children:[(0,h.jsx)("div",{className:d,children:e.icon}),(0,h.jsx)(r.A,{as:"h3",className:p,children:(0,h.jsx)(s.A,{id:e.titleId,children:e.title})}),(0,h.jsx)("p",{className:m,children:(0,h.jsx)(s.A,{id:e.descriptionId,children:e.description})})]},i)))})]})})}},46258:(e,i,t)=>{t.d(i,{A:()=>_});t(96540);var s=t(34164),r=t(50539);const n="comparisonSection_xGN0",o="responsiveContainer_LWkJ",a="sectionTitle_WEFW",l="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",p="featureHeader_MZvb",m="featureCell_N7ZK",h="funblocksHeader_W9br",u="funblocksCell_mVK7",f="comparisonNote_BsSN",x="scrollIndicator_qOFX";var g=t(9303),j=t(74848);const _=function(e){let{page:i="homepage",customData:t=null,titleTranslateId:_=null,descriptionTranslateId:A=null,noteTranslateId:w=null,competitors:b=null}=e;const k={funblocks:{label:(0,j.jsx)(r.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,j.jsx)(r.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,j.jsx)(r.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,j.jsx)(r.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},v=b||k,I=[{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,j.jsx)(r.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],y=t||I;return(0,j.jsx)("section",{id:"comparison",className:n,children:(0,j.jsxs)("div",{className:(0,s.A)("container",o),children:[(0,j.jsx)(g.A,{as:"h2",className:a,children:(0,j.jsx)(r.A,{id:_||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,j.jsx)("p",{className:l,children:(0,j.jsx)(r.A,{id:A||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,j.jsx)("div",{className:x,children:(0,j.jsx)(r.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,j.jsx)("div",{className:c,children:(0,j.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(v).length},children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{children:[(0,j.jsx)("th",{className:p,children:(0,j.jsx)(r.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(v).map((e=>{let[i,t]=e;return(0,j.jsx)("th",{className:t.isHighlighted?h:void 0,children:t.label},i)}))]})}),(0,j.jsx)("tbody",{children:y.map(((e,i)=>(0,j.jsxs)("tr",{children:[(0,j.jsx)("td",{className:m,children:e.feature}),Object.entries(v).map((i=>{let[t,s]=i;return(0,j.jsx)("td",{className:s.isHighlighted?u:void 0,children:!0===e[t]?"\u2705":!1===e[t]?"\u274c":e[t]},t)}))]},i)))})]})}),(0,j.jsx)("div",{className:f,children:(0,j.jsx)("p",{children:(0,j.jsx)(r.A,{id:w||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},46657:(e,i,t)=>{t.r(i),t.d(i,{default:()=>z});var s=t(96540),r=t(34164),n=t(56289),o=t(40797),a=t(30300),l=t(50539),c=t(9303),d=t(21099);const p={pageSection:"pageSection_C96F",hero:"hero_T5HJ",heroBadge:"heroBadge_PLzU",heroContent:"heroContent_aL0w",heroButtons:"heroButtons_sNAT",btnPrimary:"btnPrimary_tzPG",btnSecondary:"btnSecondary_zDac",heroStats:"heroStats_H3zZ",heroStat:"heroStat_rKD5",heroStatNumber:"heroStatNumber_bu_j",heroStatLabel:"heroStatLabel_zIwP",heroImageContainer:"heroImageContainer_cHJD",heroImageWrapper:"heroImageWrapper_Bkor",heroImage:"heroImage_y27M",heroImageOverlay:"heroImageOverlay_mU_D",heroImageOverlayText:"heroImageOverlayText_H_JB",heroRow:"heroRow_Aaj0",pricingCards:"pricingCards_NYQI",pricingCard:"pricingCard_ZjJb",pricingCardTitle:"pricingCardTitle_ivXU",pricingFeatures:"pricingFeatures_J5QN",pricingNote:"pricingNote_hOVu",featureSection:"featureSection_X7aJ",pricingContainer:"pricingContainer_ea93",featureGrid:"featureGrid_pyAd",featureContent:"featureContent_V9LY",featureImageWrapper:"featureImageWrapper_q4r_",sectionHeading:"sectionHeading_IHEW",sectionTitle:"sectionTitle_S00x",sectionDescription:"sectionDescription_lB7n",workflowSteps:"workflowSteps_ddV7",workflowStep:"workflowStep_IwFB",stepNumber:"stepNumber_aEuY",stepTitle:"stepTitle_UMQw",stepDescription:"stepDescription_vsMs",featureBadge:"featureBadge_MpsD",featureTitle:"featureTitle_fjM9",featureDescription:"featureDescription_V1bR",featureList:"featureList_iAjF",featureIcon:"featureIcon_bmtk",featureImageContainer:"featureImageContainer_p_wY",featureImage:"featureImage_uaqd",featureImageOverlay:"featureImageOverlay_t5gE",featureImageOverlayText:"featureImageOverlayText_k4Vp",ctaContainer:"ctaContainer_jwKT",ctaTitle:"ctaTitle_M1Wb",ctaDescription:"ctaDescription_JgyB",ctaButton:"ctaButton_NVu7"};var m=t(26167),h=t(87263),u=t(51971),f=t(79912),x=t(81896),g=t(78905),j=t(68154),_=t(74848);const A=function(){const{siteConfig:e}=(0,o.A)(),{url:i}=e,t={"@context":"https://schema.org","@type":"Organization",name:"FunBlocks AI",url:i,logo:`${i}/img/logo.png`,sameAs:["https://twitter.com/funblocksai","https://www.linkedin.com/company/funblocksai"]};return(0,_.jsxs)(j.m,{children:[(0,_.jsx)("script",{type:"application/ld+json",children:JSON.stringify(t)}),(0,_.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Prompt Optimizer Extension",applicationCategory:"BrowserExtension",operatingSystem:"Chrome, Edge",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"Optimize your prompts for ChatGPT, Claude, Gemini, Perplexity, DeepSeek and more. Get better AI answers with improved questions and instructions. Generate related questions and explore topics more deeply with our browser extension.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.7",ratingCount:"850"}})}),(0,_.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is the FunBlocks AI Prompt Optimizer Extension?",acceptedAnswer:{"@type":"Answer",text:"The FunBlocks AI Prompt Optimizer Extension is a browser plugin for Chrome and Edge that helps optimize your prompts for AI chat applications like ChatGPT, Claude, Gemini, Perplexity, and DeepSeek. It improves your questions and instructions to get better answers from AI."}},{"@type":"Question",name:"How does the Prompt Optimizer Extension work?",acceptedAnswer:{"@type":"Answer",text:"The extension adds a widget below the input box in AI chat applications. You can choose to optimize your questions or instructions, and the extension will generate improved versions. It also adds tools to explore related questions and topics based on your conversation."}},{"@type":"Question",name:"Is the Prompt Optimizer Extension free to use?",acceptedAnswer:{"@type":"Answer",text:"New users get 30 free optimizations, and all users receive 10 free optimizations daily. The extension is also included in all FunBlocks AI subscription plans for unlimited usage."}},{"@type":"Question",name:"Which AI platforms does the Prompt Optimizer Extension support?",acceptedAnswer:{"@type":"Answer",text:"The extension currently supports ChatGPT, Claude, Gemini, Perplexity, DeepSeek and other popular AI chat applications. We continuously add support for more platforms."}},{"@type":"Question",name:"How does the dynamic form feature work for missing information?",acceptedAnswer:{"@type":"Answer",text:'When you select "Optimize Instruction," our AI analyzes your prompt to identify any missing information that would be crucial for a high-quality response. If gaps are detected, the extension generates a custom form with specific fields for the missing details.'}},{"@type":"Question",name:"Can I customize the optimization parameters for different types of content?",acceptedAnswer:{"@type":"Answer",text:"Yes, we offer customization options for different use cases. You can set preferences for academic writing, creative content, technical documentation, business communication, and more. Premium subscribers have access to additional customization options."}}]})})]})};var w=t(46258),b=(t(74648),t(32427));function k(e){let{setShowImageSrc:i}=e;return(0,_.jsx)("section",{id:"hero",className:(0,r.A)(p.hero,p.pageSection),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:(0,_.jsx)("div",{className:"container",children:(0,_.jsxs)("div",{className:p.heroRow,children:[(0,_.jsxs)("div",{className:p.heroContent,style:{flex:1,minWidth:0},children:[(0,_.jsx)("div",{className:p.heroBadge,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.badge",children:"NEW BROWSER EXTENSION"})}),(0,_.jsx)(c.A,{as:"h1",style:{color:"white",textShadow:"0 2px 4px rgba(0, 0, 0, 0.3)"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.title",children:"AI Prompt Optimizer & Response Analyzer"})}),(0,_.jsx)("p",{className:p.heroSubtitle,style:{color:"rgba(255, 255, 255, 0.95)",textShadow:"0 1px 2px rgba(0, 0, 0, 0.2)"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.subtitle",children:"Enhance your AI conversations with prompt optimization and critical thinking tools. Let AI help you think better, not replace your thinking."})}),(0,_.jsxs)("div",{className:p.heroButtons,children:[(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary),to:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.trial",children:"Download Extension for FREE"})}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnSecondary),to:"#how-it-works",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.learn_more",children:"See How It Works"})})]}),(0,_.jsxs)("div",{className:p.heroStats,children:[(0,_.jsxs)("div",{className:p.heroStat,children:[(0,_.jsx)("span",{className:p.heroStatNumber,children:"30+"}),(0,_.jsx)("span",{className:p.heroStatLabel,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.stat1",children:"Free Optimizations"})})]}),(0,_.jsxs)("div",{className:p.heroStat,children:[(0,_.jsx)("span",{className:p.heroStatNumber,children:"5"}),(0,_.jsx)("span",{className:p.heroStatLabel,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.stat2",children:"Supported AI Platforms"})})]}),(0,_.jsxs)("div",{className:p.heroStat,children:[(0,_.jsx)("span",{className:p.heroStatNumber,children:"4.7\u2605"}),(0,_.jsx)("span",{className:p.heroStatLabel,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.stat3",children:"User Rating"})})]})]})]}),(0,_.jsx)("div",{className:p.heroImageContainer,style:{flex:1,minWidth:0,display:"flex",justifyContent:"center"},children:(0,_.jsxs)("div",{className:p.heroImageWrapper,children:[(0,_.jsx)("img",{className:p.heroImage,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_hero.png"),id:"prompt-optimizer-overview",alt:"FunBlocks AI Prompt Optimizer Extension interface",src:"/img/portfolio/fullsize/prompt_optimizer_hero.png"}),(0,_.jsx)("div",{className:p.heroImageOverlay,children:(0,_.jsx)("span",{className:p.heroImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.hero.image_caption",children:"Click to enlarge"})})})]})})]})})})}function v(e){let{setShowImageSrc:i}=e;return(0,_.jsx)("section",{id:"how-it-works",className:p.featureSection,children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsxs)("div",{className:p.sectionHeading,children:[(0,_.jsx)(c.A,{as:"h2",className:p.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.title",children:"How the Prompt Optimizer Works"})}),(0,_.jsx)("p",{className:p.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.description",children:"Our browser extension seamlessly integrates with leading AI platforms like ChatGPT, Claude, and Gemini, providing powerful prompt optimization and critical thinking tools right where you need them"})})]}),(0,_.jsxs)("div",{className:p.workflowSteps,children:[(0,_.jsxs)("div",{className:p.workflowStep,children:[(0,_.jsx)("div",{className:p.stepNumber,children:"1"}),(0,_.jsx)(c.A,{as:"h3",className:p.stepTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step1.title",children:"Install the Extension"})}),(0,_.jsx)("p",{className:p.stepDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step1.description",children:"Add the Prompt Optimizer to Chrome or Edge in just a few clicks. No complex setup required."})})]}),(0,_.jsxs)("div",{className:p.workflowStep,children:[(0,_.jsx)("div",{className:p.stepNumber,children:"2"}),(0,_.jsx)(c.A,{as:"h3",className:p.stepTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step2.title",children:"Visit Your Favorite AI Chat"})}),(0,_.jsx)("p",{className:p.stepDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step2.description",children:"Open ChatGPT, Claude, Gemini, or any supported AI platform and see our tools automatically appear."})})]}),(0,_.jsxs)("div",{className:p.workflowStep,children:[(0,_.jsx)("div",{className:p.stepNumber,children:"3"}),(0,_.jsx)(c.A,{as:"h3",className:p.stepTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step3.title",children:"Optimize Your Prompts"})}),(0,_.jsx)("p",{className:p.stepDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.step3.description",children:"Use our one-click tools to transform basic questions into powerful, precise prompts that get better results."})})]})]}),(0,_.jsxs)("div",{className:p.featureGrid,style:{flexDirection:"row-reverse"},children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.badge",children:"FEATURE HIGHLIGHT"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.title",children:"Smart Question & Instruction Optimization"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.description",children:"The extension adds a Prompt Optimizer widget below the input box in AI chat applications. Our AI analyzes your prompts and suggests improvements based on proven prompt engineering techniques."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.point1",children:'Choose "Optimize Question" to generate 5 more accurate, specific, or different perspectives on your question'})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.point2",children:'Select "Optimize Instruction" to clarify your prompt based on your intent and core needs'})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.point3",children:"If your instruction needs more information, a dynamic form will appear to help you provide the necessary details"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.point4",children:"Replace your original prompt with the optimized version with a single click"})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_hero.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-question",alt:"FunBlocks AI Prompt Optimizer question optimization interface",src:"/img/portfolio/fullsize/prompt_optimizer_hero.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.featureGrid,children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,style:{backgroundColor:"#e0f2ea",color:"green"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.badge",children:"REFINED QUESTIONS"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.title",children:"Well-Asked Is Half-Solved"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.description",children:"Better input means better output. Our tools guide you to write sharper prompts for clearer, more reliable AI results."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.point1",children:"AI helps you rephrase your question to be clearer and more effective"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.point2",children:"Refined prompts lead to more accurate and relevant AI responses"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.point3",children:"Improve outcomes across writing, coding, research, and more"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature5.point4",children:"Learn from the optimized prompts to enhance your own questioning skills"})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_refined_questions.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-question",alt:"FunBlocks AI Prompt Optimizer question optimization generated related topics and questions",src:"/img/portfolio/fullsize/prompt_optimizer_refined_questions.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.featureGrid,children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,style:{backgroundColor:"#e0f2ea",color:"green"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.badge",children:"SMART FORM"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.title",children:"Dynamic Form for Missing Information"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.description",children:"If your instruction is missing key details, the Prompt Optimizer will automatically display a dynamic form. This form guides you to provide the necessary information, ensuring your prompt is clear and complete for the best AI results."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83d\udcdd"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.point1",children:"Instantly detects missing context or requirements in your prompt"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u27a1\ufe0f"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.point2",children:"Presents a simple form to fill in the gaps\u2014no guesswork needed"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2705"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature3.point3",children:"Your optimized prompt is generated with all the required details for better AI answers"})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_form.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-question",alt:"FunBlocks AI Prompt Optimizer question optimization dynamic form interface",src:"/img/portfolio/fullsize/prompt_optimizer_form.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.featureGrid,style:{flexDirection:"row-reverse"},children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.badge",children:"EXCLUSIVE FEATURE"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.title",children:"Critical Thinking Assistant"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.description",children:"Our new Critical Thinking Assistant dropdown menu provides powerful tools to help you think more deeply about AI responses and explore topics from multiple perspectives."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83e\udde0"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.point1",children:'Access the "Critical Thinking Assistant" dropdown menu in the toolbar of each AI response'})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83d\udd0d"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.point2",children:'Use "Critical Analysis" to evaluate AI responses for logical consistency, potential biases, and alternative perspectives'})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2753"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.point3",children:'Generate "Related Questions" to explore deeper aspects of the topic you might not have considered'})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83d\udcda"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.point4",children:'Discover "Related Topics" to broaden your understanding and explore connected subjects'})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_related.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-related",alt:"FunBlocks AI Prompt Optimizer related questions and topics interface",src:"/img/portfolio/fullsize/prompt_optimizer_related.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature2.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.featureGrid,children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,style:{backgroundColor:"#e0f2ea",color:"green"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature4.badge",children:"UNLIMITED EXPLORATION"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature4.title",children:"Generated Exploration Space"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature4.description",children:"Our unique exploration tools that help you discover related questions and topics you might not have considered."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature4.point1",children:"Explore deeper insights with follow-up questions"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature4.point2",children:"Discover broader perspectives through related topics"})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_related_topics.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-question",alt:"FunBlocks AI Prompt Optimizer question optimization generated related topics and questions",src:"/img/portfolio/fullsize/prompt_optimizer_related_topics.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.feature1.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.featureGrid,children:[(0,_.jsxs)("div",{className:p.featureContent,children:[(0,_.jsx)("div",{className:p.featureBadge,style:{backgroundColor:"#fff3cd",color:"#856404"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.badge",children:"CRITICAL ANALYSIS"})}),(0,_.jsx)(c.A,{as:"h3",className:p.featureTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.title",children:"Evaluate AI Responses with Critical Thinking"})}),(0,_.jsx)("p",{className:p.featureDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.description",children:"Our Critical Analysis feature helps you think more deeply about AI responses, evaluating them for logical consistency, potential biases, and alternative perspectives."})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83d\udd0d"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.point1",children:"Analyze AI responses for logical consistency and factual accuracy"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2696\ufe0f"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.point2",children:"Identify potential biases and limitations in AI-generated content"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83e\udd14"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.point3",children:"Explore alternative perspectives and counterarguments"})})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\ud83d\udca1"}),(0,_.jsx)("div",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.point4",children:"Develop stronger critical thinking skills for better AI collaboration"})})]})]})]}),(0,_.jsx)("div",{className:p.featureImageWrapper,children:(0,_.jsxs)("div",{className:p.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/prompt_optimizer_critical_analyzer.png"),children:[(0,_.jsx)("img",{className:p.featureImage,id:"prompt-optimizer-critical-analysis",alt:"FunBlocks AI Prompt Optimizer critical analysis interface",src:"/img/portfolio/fullsize/prompt_optimizer_critical_analyzer.png"}),(0,_.jsx)("div",{className:p.featureImageOverlay,children:(0,_.jsx)("span",{className:p.featureImageOverlayText,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.critical_analysis.image_caption",children:"Click to enlarge"})})})]})})]}),(0,_.jsxs)("div",{className:p.ctaContainer,children:[(0,_.jsx)(c.A,{as:"h3",className:p.ctaTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.cta.title",children:"Ready to transform your AI conversations?"})}),(0,_.jsx)("p",{className:p.ctaDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.cta.description",children:"Join thousands of users who are getting better results from AI with optimized prompts"})}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary,p.ctaButton),to:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.how_it_works.cta.button",children:"Download Extension for FREE"})})]})]})})}function I(){return(0,_.jsx)("section",{id:"why-it-matters",className:d.A.featureSection,style:{backgroundColor:"honeydew"},children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.title",children:"Why Good Prompts Matter"})}),(0,_.jsx)("p",{className:d.A.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.description",children:"In the age of generative AI, the ability to ask good questions is a crucial skill"})}),(0,_.jsxs)("div",{className:d.A.benefitsContainer,children:[(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udd11"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point1.title",children:"The Key to Better Answers"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point1.description",children:"AI models have vast knowledge and powerful capabilities, but they need clear, specific instructions to deliver their best results. Good prompts unlock their full potential."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udca1"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point2.title",children:"A Critical Human Skill"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point2.description",children:"As AI becomes more integrated into our work and lives, the ability to ask good questions and provide clear instructions becomes an increasingly valuable human skill."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\ude80"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point3.title",children:"Competitive Advantage"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.why_it_matters.point3.description",children:"Those who can effectively communicate with AI will have a significant advantage in productivity, creativity, and problem-solving in the AI-powered future."})})]})]})]})})}function y(){return(0,_.jsx)("section",{id:"thinking-enhancement",className:d.A.featureSection,style:{backgroundColor:"#f8f9ff"},children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsxs)("div",{className:d.A.sectionHeading,children:[(0,_.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.title",children:"Beyond Prompt Optimization: Enhancing Your Thinking Abilities"})}),(0,_.jsx)("p",{className:d.A.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.description",children:"Our extension doesn't just optimize prompts\u2014it actively develops your critical thinking skills and maximizes AI value through enhanced human-AI collaboration. Thinking matters in the era of AI."})})]}),(0,_.jsxs)("div",{className:`${d.A.twoColumnGrid}`,style:{margin:"0 auto"},children:[(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83c\udfaf"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point1.title",children:"Master the Art of Asking Better Questions"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point1.description",children:"Good questions are the key to solving problems. Questioning ability is a crucial human thinking skill. Our AI helps you optimize your questions and practice this essential skill through guided improvement."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udcdd"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point2.title",children:"Clearer Problem Description"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point2.description",children:"Learn to describe problems more clearly and precisely through guided optimization, developing better communication skills that benefit all your AI interactions."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udd0d"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point3.title",children:"Broader Perspective Exploration"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point3.description",children:"Generate related questions and topics based on your input or AI responses, encouraging you to explore subjects from multiple angles and discover new insights."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\u2696\ufe0f"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point4.title",children:"Critical Analysis Skills"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.point4.description",children:"Perform critical analysis on AI responses to improve your analytical and judgment abilities, helping you evaluate information quality and identify potential biases."})})]})]}),(0,_.jsxs)("div",{className:d.A.ctaContainer,style:{marginTop:"3rem",textAlign:"center",backgroundColor:"#e6f3ff",padding:"2rem",borderRadius:"12px"},children:[(0,_.jsx)(c.A,{as:"h3",style:{color:"#1a365d",marginBottom:"1rem"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.cta.title",children:"Enhance Your Thinking, Not Replace It"})}),(0,_.jsx)("p",{style:{color:"#2d3748",marginBottom:"1.5rem",fontSize:"1.1rem"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.cta.description",children:"After using our extension, you'll not only get better results from ChatGPT, Claude, and other AI tools, but also develop stronger thinking abilities. Because thinking matters in the era of AI."})}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary),to:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",style:{fontSize:"1.1rem",padding:"0.75rem 2rem"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.thinking_enhancement.cta.button",children:"Start Thinking Better with AI"})})]})]})})}function N(e){let{bg:i="#fff"}=e;return(0,_.jsx)("section",{id:"pricing",className:d.A.featureSection,style:{backgroundColor:i},children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.title",children:"Try It For Free"})}),(0,_.jsx)("p",{className:d.A.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.description",children:"Get started with our generous free trial and flexible subscription options"})}),(0,_.jsxs)("div",{className:p.pricingContainer,style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"2rem"},children:[(0,_.jsxs)("div",{className:p.pricingCards,children:[(0,_.jsxs)("div",{className:p.pricingCard,children:[(0,_.jsx)(c.A,{as:"h3",className:p.pricingTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.free_trial.title",children:"Free Trial"})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.free_trial.feature1",children:"30 free optimizations for new users"})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.free_trial.feature2",children:"10 free optimizations daily"})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.free_trial.feature3",children:"Access to all core features"})]})]}),(0,_.jsx)("p",{className:p.pricingNote,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.free_trial.note",children:"No credit card required"})})]}),(0,_.jsxs)("div",{className:p.pricingCard,children:[(0,_.jsx)(c.A,{as:"h3",className:d.A.pricingTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.subscription.title",children:"FunBlocks AI Plans"})}),(0,_.jsxs)("ul",{className:p.featureList,children:[(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,style:{backgroundColor:"limegreen"},children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.subscription.feature1",children:"Included in all FunBlocks AI subscription plans"})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,style:{backgroundColor:"limegreen"},children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.subscription.feature2",children:"Choose the plan that fits your needs"})]}),(0,_.jsxs)("li",{children:[(0,_.jsx)("div",{className:p.featureIcon,style:{backgroundColor:"limegreen"},children:"\u2713"}),(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.subscription.feature3",children:"Access to the entire FunBlocks AI ecosystem"})]})]}),(0,_.jsx)("p",{className:p.pricingNote,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.subscription.note",children:"See pricing page for details"})})]})]}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary),to:"/pricing",style:{marginTop:"1.5rem",minWidth:200,textAlign:"center"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.pricing.button",children:"View Pricing Plans"})})]})]})})}function z(){(0,o.A)();const[e,i]=(0,s.useState)(null);return(0,_.jsxs)(a.A,{title:(0,l.T)({id:"prompt_optimizer.head.title",message:"FunBlocks AI Prompt Optimizer & Critical Thinking Assistant - Enhance AI Conversations"}),description:(0,l.T)({id:"prompt_optimizer.head.description",message:"Optimize your prompts and enhance critical thinking with our AI assistant for ChatGPT, Claude, Gemini, and more. Features prompt optimization, critical analysis, related questions, and topic exploration to help you think better with AI."}),keywords:(0,l.T)({id:"prompt_optimizer.head.keywords",message:"AI prompt optimizer, critical thinking assistant, ChatGPT prompts, Claude prompts, AI conversation enhancement, prompt engineering, critical analysis, AI assistant, browser extension, better AI answers"}),children:[(0,_.jsx)(A,{}),(0,_.jsx)(k,{setShowImageSrc:i}),(0,_.jsxs)("main",{children:[(0,_.jsx)(b.A,{page:"prompt_optimizer",backgroundColor:"honeydew",customBenefits:[{icon:"\ud83c\udfaf",titleId:"prompt_optimizer.benefits.benefit1.title",title:"More Accurate Responses",descriptionId:"prompt_optimizer.benefits.benefit1.description",description:"Get precisely what you need from AI with optimized prompts that clearly communicate your intent and requirements."},{icon:"\u23f1\ufe0f",titleId:"prompt_optimizer.benefits.benefit2.title",title:"Save Time & Effort",descriptionId:"prompt_optimizer.benefits.benefit2.description",description:"Eliminate back-and-forth clarifications by starting with well-crafted prompts that address all necessary details."},{icon:"\ud83d\udca1",titleId:"prompt_optimizer.benefits.benefit3.title",title:"Improve Your Prompting Skills",descriptionId:"prompt_optimizer.benefits.benefit3.description",description:"Learn by example as you see how the extension transforms basic prompts into powerful instructions."},{icon:"\ud83d\ude80",titleId:"prompt_optimizer.benefits.benefit4.title",title:"Critical Thinking Enhancement",descriptionId:"prompt_optimizer.benefits.benefit4.description",description:"Develop critical thinking skills with AI-powered analysis tools that help you evaluate responses and think more deeply."},{icon:"\ud83d\udd0d",titleId:"prompt_optimizer.benefits.benefit7.title",title:"Deeper Exploration",descriptionId:"prompt_optimizer.benefits.benefit7.description",description:"Discover related questions and topics to explore subjects more thoroughly and from different perspectives."},{icon:"\ud83d\udd0c",titleId:"prompt_optimizer.benefits.benefit5.title",title:"Works With Your Favorite AI Tools",descriptionId:"prompt_optimizer.benefits.benefit5.description",description:"Compatible with ChatGPT, Claude, Gemini, Perplexity, DeepSeek and other popular AI chat applications."}]}),(0,_.jsx)("section",{id:"less-is-more",className:d.A.featureSection,style:{backgroundColor:"#f8f9fa"},children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsxs)("div",{className:d.A.sectionHeading,children:[(0,_.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.title",children:"Get More from AI with Less Instructions"})}),(0,_.jsx)("p",{className:d.A.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.description",children:"Sometimes less is more when it comes to AI prompts. Start with your goal and let AI do the heavy lifting."})})]}),(0,_.jsxs)("div",{className:d.A.benefitsContainer,children:[(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83c\udfaf"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point1.title",children:"Focus on Your Goal"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point1.description",children:"Instead of detailed instructions, simply state what you want to achieve. Let AI analyze and plan the best approach."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\ude80"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point2.title",children:"Break Free from Limitations"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point2.description",children:"Detailed instructions can limit AI's creativity. By focusing on goals, you open doors to solutions you might not have considered."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udca1"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point3.title",children:"Let AI Do the Heavy Lifting"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.point3.description",children:"The Prompt Optimizer helps you start with simple goals, then uses AI to expand and enhance your prompts for better results."})})]})]}),(0,_.jsxs)("div",{className:d.A.ctaContainer,style:{marginTop:"2rem",textAlign:"center"},children:[(0,_.jsx)("p",{className:d.A.ctaDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.cta",children:"Experience how less can truly be more with the Prompt Optimizer"})}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary),to:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",style:{marginTop:"1rem"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.less_is_more.button",children:"Try It Now"})})]})]})}),(0,_.jsx)("section",{id:"lazy-prompting",className:d.A.featureSection,style:{backgroundColor:"#f0f7ff"},children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsxs)("div",{className:d.A.sectionHeading,children:[(0,_.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.title",children:"Lazy Prompting: A New Approach to AI Interaction"})}),(0,_.jsx)("p",{className:d.A.sectionDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.description",children:"Lazy prompting is an innovative method that starts with minimal context and gradually adds more as needed, allowing AI to surprise us with unexpected insights and solutions."})})]}),(0,_.jsxs)("div",{className:d.A.benefitsContainer,children:[(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83c\udfaf"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point1.title",children:"Start Simple"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point1.description",children:"Begin with a basic idea or question, letting the AI explore possibilities without being constrained by detailed instructions."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udca1"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point2.title",children:"Discover the Unexpected"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point2.description",children:"Allow AI to surprise you with solutions and perspectives you might not have considered with traditional prompting methods."})})]}),(0,_.jsxs)("div",{className:d.A.benefitCard,children:[(0,_.jsxs)("div",{className:d.A.cardTitle,children:[(0,_.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\ude80"}),(0,_.jsx)(c.A,{as:"h4",children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point3.title",children:"Optimize as Needed"})})]}),(0,_.jsx)("p",{children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.point3.description",children:"Use the Prompt Optimizer to enhance and refine your prompts when you need more specific guidance or detailed responses."})})]})]}),(0,_.jsxs)("div",{className:d.A.ctaContainer,style:{marginTop:"2rem",textAlign:"center"},children:[(0,_.jsx)("p",{className:d.A.ctaDescription,children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.cta",children:"Experience the power of lazy prompting with FunBlocks AI"})}),(0,_.jsx)(n.A,{className:(0,r.A)("button",p.btnPrimary),to:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",style:{marginTop:"1rem"},children:(0,_.jsx)(l.A,{id:"prompt_optimizer.lazy_prompting.button",children:"Try It Now"})})]})]})}),(0,_.jsx)(v,{setShowImageSrc:i}),(0,_.jsx)(I,{}),(0,_.jsx)(y,{}),(0,_.jsx)(w.A,{page:"prompt_optimizer",titleTranslateId:"prompt_optimizer.comparison.title",descriptionTranslateId:"prompt_optimizer.comparison.description",noteTranslateId:"prompt_optimizer.comparison.note",competitors:{funblocks:{label:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.funblocksHeader",children:"FunBlocks AI Prompt Optimizer"}),isHighlighted:!0},chatgpt:{label:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.chatgptHeader",children:"Standard ChatGPT"}),isHighlighted:!1},promptEngineering:{label:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.promptEngineeringHeader",children:"Manual Prompt Engineering"}),isHighlighted:!1},otherExtensions:{label:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.otherExtensionsHeader",children:"Other AI Extensions"}),isHighlighted:!1}},customData:[{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature1",children:"One-Click Prompt Optimization"}),funblocks:!0,chatgpt:!1,promptEngineering:!1,otherExtensions:"Limited"},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature2",children:"Dynamic Form for Missing Information"}),funblocks:!0,chatgpt:!1,promptEngineering:!1,otherExtensions:!1},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature3",children:"Related Questions Generation"}),funblocks:!0,chatgpt:!1,promptEngineering:!1,otherExtensions:"Some"},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature4",children:"Related Topics Exploration"}),funblocks:!0,chatgpt:!1,promptEngineering:!1,otherExtensions:"Rare"},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature5",children:"Multi-Platform Support"}),funblocks:!0,chatgpt:!1,promptEngineering:!0,otherExtensions:"Limited"},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature6",children:"Improves Prompting Skills"}),funblocks:!0,chatgpt:!1,promptEngineering:!0,otherExtensions:"Some"},{feature:(0,_.jsx)(l.A,{id:"prompt_optimizer.comparison.feature7",children:"Free Daily Usage"}),funblocks:!0,chatgpt:!0,promptEngineering:!0,otherExtensions:"Varies"}]}),(0,_.jsx)(N,{bg:"honeydew"}),(0,_.jsx)(u.A,{avatars:["\ud83d\udc68\u200d\ud83d\udcbb","\ud83d\udc69\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udf93","\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc69\u200d\ud83c\udf93"],page:"prompt_optimizer"}),(0,_.jsx)(g.A,{toUrl:"https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh",page:"prompt_optimizer",customButtonText:(0,_.jsx)(l.A,{id:"prompt_optimizer.cta.button",children:"Download Extension for FREE"})}),(0,_.jsx)(h.A,{page:"prompt_optimizer",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8","q9","q10","q10e","q11","q12"]})]}),(0,_.jsx)(m.A,{}),e&&(0,_.jsx)(f.A,{imageSrc:e,setImageSrc:i}),(0,_.jsx)(x.A,{page:"prompt_optimizer"})]})}},51971:(e,i,t)=>{t.d(i,{A:()=>f});var s=t(96540),r=t(50539);const n="sectionTitle_pRDY",o="sectionDescription_GyST",a="benefitsContainer_jm1z",l="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",p="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var h=t(9303),u=(t(56289),t(74848));const f=function(e){let{page:i,avatars:t}=e;const f=(0,s.useMemo)((()=>t.map(((e,t)=>({avatar:e,nameId:`${i}.testimonials.user${t+1}.name`,roleId:`${i}.testimonials.user${t+1}.role`,textId:`${i}.testimonials.user${t+1}.text`})))),[i,t]);return(0,u.jsx)("section",{id:"testimonials",className:l,children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)(h.A,{as:"h2",className:n,children:(0,u.jsx)(r.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,u.jsx)("p",{className:o,children:(0,u.jsx)(r.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,u.jsx)("div",{className:a,children:f?.map(((e,i)=>(0,u.jsxs)("div",{className:c,children:[(0,u.jsxs)("div",{className:d,children:[(0,u.jsx)("div",{className:p,children:(0,u.jsx)("span",{children:e.avatar})}),(0,u.jsxs)("div",{className:m,children:[(0,u.jsx)("h4",{children:(0,u.jsx)(r.A,{id:e.nameId,children:e.nameId})}),(0,u.jsx)("p",{children:(0,u.jsx)(r.A,{id:e.roleId,children:e.roleId})})]})]}),(0,u.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,u.jsx)("p",{children:(0,u.jsx)(r.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},74648:(e,i,t)=>{t.d(i,{A:()=>_});t(96540);var s=t(34164),r=t(50539);const n="videoSection_rFsi",o="sectionTitle_QAn3",a="sectionDescription_TQMg",l="videoContainer_bfos",c="videoWrapper__9tu",d="videoFrame_FnQH",p="videoFeatures_TNWz",m="featureItem_IV9D",h="featureIcon_ehMe",u="videoCta_SQLf",f="btn_z1hz";var x=t(9303),g=t(56289),j=t(74848);const _=function(e){let{page:i="homepage",videoId:t="tPjuWOjpJIs",titleTranslateId:_=null,descriptionTranslateId:A=null,ctaTranslateId:w=null,ctaUrl:b="https://app.funblocks.net/#/login?source=flow",bg:k="#f0f7ff",customFeatures:v=null}=e;const I=[{icon:"\ud83d\udd0d",title:(0,j.jsx)(r.A,{id:`${i}.video.feature1.title`,children:"Explore"}),description:(0,j.jsx)(r.A,{id:`${i}.video.feature1.description`,children:"See how to explore complex topics visually with AI assistance"})},{icon:"\ud83c\udf1f",title:(0,j.jsx)(r.A,{id:`${i}.video.feature2.title`,children:"Think"}),description:(0,j.jsx)(r.A,{id:`${i}.video.feature2.description`,children:"Learn how to enhance your thinking with visual frameworks"})},{icon:"\u2728",title:(0,j.jsx)(r.A,{id:`${i}.video.feature3.title`,children:"Create"}),description:(0,j.jsx)(r.A,{id:`${i}.video.feature3.description`,children:"Discover how to transform ideas into professional deliverables"})}],y=v||I;return(0,j.jsx)("section",{id:"video-demo",className:n,style:{background:k},children:(0,j.jsxs)("div",{className:"container",children:[(0,j.jsx)(x.A,{as:"h2",className:o,children:(0,j.jsx)(r.A,{id:_||`${i}.video.title`,children:"See FunBlocks AIFlow in Action"})}),(0,j.jsx)("p",{className:a,children:(0,j.jsx)(r.A,{id:A||`${i}.video.description`,children:"Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate"})}),(0,j.jsx)("div",{className:l,children:(0,j.jsx)("div",{className:c,children:(0,j.jsx)("iframe",{className:d,src:`https://www.youtube.com/embed/${t}`,title:"FunBlocks AIFlow Demo",border:"0",style:{border:0},allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})}),(0,j.jsx)("div",{className:p,children:y.map(((e,i)=>(0,j.jsxs)("div",{className:m,children:[(0,j.jsx)("div",{className:h,children:e.icon}),(0,j.jsx)("h3",{children:e.title}),(0,j.jsx)("p",{children:e.description})]},i)))}),(0,j.jsx)("div",{className:u,children:(0,j.jsx)(g.A,{className:(0,s.A)("button",f),to:"#",onClick:()=>window.open(b,"_blank"),children:(0,j.jsx)(r.A,{id:w||`${i}.video.cta`,children:"Try It Yourself"})})})]})})}},78905:(e,i,t)=>{t.d(i,{A:()=>m});t(96540);var s=t(34164),r=t(50539);const n="btn_4iM2",o="ctaButtons_Cfhe",a="ctaBtn_Hq_p",l="ctaSection_vQl5";var c=t(9303),d=t(56289),p=t(74848);const m=function(e){let{page:i,toUrl:t,toApp:m,customButtonText:h}=e;return(0,p.jsx)("section",{id:"cta",className:l,children:(0,p.jsxs)("div",{className:"container",children:[(0,p.jsx)(c.A,{as:"h2",children:(0,p.jsx)(r.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,p.jsx)("p",{children:(0,p.jsx)(r.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,p.jsx)("div",{className:o,children:(0,p.jsx)(d.A,{className:(0,s.A)(n,a),to:t,onClick:t?void 0:()=>m(),children:h||(0,p.jsx)(r.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,t)=>{t.d(i,{A:()=>c});t(96540);var s=t(50539);const r="modal_osiT",n="modalImage_HWh8",o="close_Y6T6",a="zoomIndicator_r4Py";var l=t(74848);const c=function(e){let{imageSrc:i,setImageSrc:t}=e;const c=()=>{t(null)};return(0,l.jsxs)("div",{className:r,style:{display:"flex"},onClick:c,children:[(0,l.jsx)("span",{className:o,onClick:c,children:"\xd7"}),(0,l.jsx)("img",{className:n,src:i,alt:(0,s.T)({id:"modal.alt",message:"Enlarged view"})}),(0,l.jsx)("div",{className:a,children:(0,l.jsx)(s.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,t)=>{t.d(i,{A:()=>r});t(96540);var s=t(74848);const r=function(e){let{page:i}=e;const t=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",r=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${t?"source="+t+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:r}})})}},87263:(e,i,t)=>{t.d(i,{A:()=>x});var s=t(96540),r=t(34164),n=t(50539);const o="sectionTitle_gwu3",a="faqSection_DBlu",l="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",p="faqArrow_irh3",m="active_RDQl",h="faqAnswer_HbCX";var u=t(74848);function f(e){let{page:i,questionId:t,answerId:o}=e;const[a,l]=(0,s.useState)(!1);return(0,u.jsxs)("div",{className:(0,r.A)(c,{[m]:a}),children:[(0,u.jsxs)("div",{className:d,onClick:()=>{l(!a)},children:[(0,u.jsx)("span",{style:{fontWeight:"normal"},children:(0,u.jsx)(n.A,{id:`${i}.faq.${t}`})}),(0,u.jsx)("div",{className:p,style:{transform:a?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,u.jsx)("div",{className:h,style:{whiteSpace:"pre-line",display:a?"block":"none"},children:(0,u.jsx)(n.A,{id:`${i}.faq.${o}`})})]})}const x=function(e){let{page:i,faqIds:t}=e;return(0,u.jsx)("section",{id:"faqs",className:(0,r.A)("page-section",a),style:{backgroundColor:"var(--gray)"},children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)("h2",{className:o,children:(0,u.jsx)(n.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,u.jsx)("div",{className:l,children:t.map((e=>(0,u.jsx)(f,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);