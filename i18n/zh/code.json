{"homepage.socialProof.title": {"message": "全球专业人士的信赖之选"}, "homepage.socialProof.description": {"message": "加入数千名使用FunBlocks AI提升生产力和创造力的专业人士"}, "homepage.socialProof.users": {"message": "活跃用户"}, "homepage.socialProof.productivity": {"message": "生产力提升"}, "homepage.socialProof.countries": {"message": "覆盖国家"}, "homepage.socialProof.rating": {"message": "用户评分"}, "homepage.socialProof.companies": {"message": "创新团队的选择"}, "homepage.comparison.title": {"message": "FunBlocks AI的优势对比"}, "homepage.comparison.description": {"message": "FunBlocks AI是一个完整的一体化AI工作空间，包含头脑风暴、创意生成、AIFlow可视化思维导图，以及AI驱动的文档、幻灯片和信息图表工具，高效完成任何类型的工作"}, "homepage.comparison.featureHeader": {"message": "功能"}, "homepage.comparison.funblocksHeader": {"message": "FunBlocks AI"}, "homepage.comparison.chatbotsHeader": {"message": "类ChatGPT AI聊天机器人"}, "homepage.comparison.notionHeader": {"message": "Notion"}, "homepage.comparison.mindmapHeader": {"message": "思维导图工具"}, "homepage.comparison.feature1": {"message": "一体化AI工作空间"}, "homepage.comparison.feature2": {"message": "视觉思维与思维导图"}, "homepage.comparison.feature3": {"message": "AI驱动的文档"}, "homepage.comparison.feature4": {"message": "AI幻灯片生成"}, "homepage.comparison.feature5": {"message": "信息图表创建"}, "homepage.comparison.feature6": {"message": "多模型AI支持"}, "homepage.comparison.feature7": {"message": "思维框架"}, "homepage.comparison.feature8": {"message": "工具间无缝集成"}, "homepage.comparison.note": {"message": "FunBlocks AI提供统一的工作空间，将AI聊天机器人、文档工具和思维导图软件的优势结合到一个无缝平台中，无需在多个工具之间切换或购买多个订阅。"}, "homepage.caseStudies.title": {"message": "真实成功案例"}, "homepage.caseStudies.description": {"message": "了解组织和个人如何通过FunBlocks AI的全面工具套件改变工作方式"}, "homepage.caseStudies.case1.title": {"message": "教育机构转型"}, "homepage.caseStudies.case1.description": {"message": "一所领先大学实施了FunBlocks AI教育工具（BloomBrain、MarzanoBrain、AIFlow、AI Slides）来增强教学材料并培养学生的批判性思维能力。这套全面的工具显著提高了教学效率和学生学习成果。"}, "homepage.caseStudies.case1.result1": {"message": "高质量教学材料创建速度提升65%"}, "homepage.caseStudies.case1.result2": {"message": "学生批判性思维能力提高42%"}, "homepage.caseStudies.case1.result3": {"message": "创造性问题解决能力提升38%"}, "homepage.caseStudies.case1.result4": {"message": "视觉学习理解能力显著增强"}, "homepage.caseStudies.case2.title": {"message": "企业创新与生产力"}, "homepage.caseStudies.case2.description": {"message": "一家财富500强公司在团队中集成了FunBlocks AI，以提升创造力和工作效率。团队使用AIFlow进行新产品功能和营销活动的头脑风暴，同时利用完整的工作空间进行无缝协作和执行。"}, "homepage.caseStudies.case2.result1": {"message": "创新产品功能想法增加3倍"}, "homepage.caseStudies.case2.result2": {"message": "通过视觉协作减少58%的会议时间"}, "homepage.caseStudies.case2.result3": {"message": "跨团队协调和沟通提升45%"}, "homepage.caseStudies.case2.result4": {"message": "专业演示文稿创建速度提高70%"}, "homepage.caseStudies.case3.title": {"message": "个人成长与学习"}, "homepage.caseStudies.case3.description": {"message": "个人用户通过FunBlocks AI改变了终身学习和个人发展方式。从创建书籍思维导图和探索新主题到为社交媒体生成创意内容，用户报告知识保留和创意表达能力显著提高。"}, "homepage.caseStudies.case3.result1": {"message": "通过思维导图提高52%的书籍概念记忆"}, "homepage.caseStudies.case3.result2": {"message": "有趣主题的探索深度提高4倍"}, "homepage.caseStudies.case3.result3": {"message": "创意写作输出增加68%"}, "homepage.caseStudies.case3.result4": {"message": "社交媒体参与度显著提升"}, "homepage.caseStudies.case4.title": {"message": "内容创作者工作流"}, "homepage.caseStudies.case4.description": {"message": "专业内容创作者通过FunBlocks AI彻底改变了工作流程。除了更快的内容制作外，他们现在还能轻松创建引人入胜的信息图表、洞察卡片和视觉故事，显著提高受众参与度并使内容与众不同。"}, "homepage.caseStudies.case4.result1": {"message": "内容制作速度提高50%"}, "homepage.caseStudies.case4.result2": {"message": "视觉内容创建增加75%"}, "homepage.caseStudies.case4.result3": {"message": "信息图表和洞察卡片的参与度提高3倍"}, "homepage.caseStudies.case4.result4": {"message": "内容独特性显著提升"}, "homepage.video.title": {"message": "观看FunBlocks AIFlow实际演示"}, "homepage.video.description": {"message": "了解FunBlocks AIFlow如何改变您思考、创造和协作的方式"}, "homepage.video.feature1.title": {"message": "探索"}, "homepage.video.feature1.description": {"message": "了解如何在AI辅助下以视觉方式探索复杂主题"}, "homepage.video.feature2.title": {"message": "思考"}, "homepage.video.feature2.description": {"message": "学习如何通过视觉框架增强思维能力"}, "homepage.video.feature3.title": {"message": "创造"}, "homepage.video.feature3.description": {"message": "发现如何将想法转化为专业成果"}, "homepage.video.cta": {"message": "亲自体验"}, "ai101.title": {"message": "生成式人工智能时代"}, "ai101.subtitle": {"message": "面向大学教师的专题讲座"}, "ai101.slide1.title": {"message": "人工智能发展简史"}, "ai101.slide1.subtitle": {"message": "从概念到现实的70年征程"}, "ai101.slide2.title": {"message": "为什么是现在？"}, "ai101.slide2.subtitle": {"message": "三大要素的完美汇聚"}, "ai101.slide3.title": {"message": "概念辨析"}, "ai101.slide3.subtitle": {"message": "LLM、GenAI、AGI的关系"}, "ai101.slide4.title": {"message": "生成式人工智能的特点"}, "ai101.slide4.subtitle": {"message": "从识别到创造的跨越"}, "ai101.slide5.title": {"message": "核心学习技术解析"}, "ai101.slide5.subtitle": {"message": "理解AI如何\"学习\""}, "ai101.slide6.title": {"message": "AI训练的三个关键阶段"}, "ai101.slide6.subtitle": {"message": "从原始到智能的蜕变"}, "ai101.slide7.title": {"message": "神经网络：模仿大脑的智能"}, "ai101.slide7.subtitle": {"message": "从生物启发到人工实现"}, "ai101.slide8.title": {"message": "AI的数学基础"}, "ai101.slide8.subtitle": {"message": "概率世界中的智能涌现"}, "ai101.slide9.title": {"message": "关键概念解析"}, "ai101.slide9.subtitle": {"message": "理解AI的基本要素"}, "ai101.slide10.title": {"message": "智能的本质：信息压缩？"}, "ai101.slide10.subtitle": {"message": "从数据中提取规律"}, "ai101.slide11.title": {"message": "Scaling Law：规模的魔力"}, "ai101.slide11.subtitle": {"message": "更大就是更强？"}, "ai101.slide12.title": {"message": "AI真的\"懂\"吗？"}, "ai101.slide12.subtitle": {"message": "统计模式 vs 真正理解"}, "ai101.slide13.title": {"message": "能否超越人类？"}, "ai101.slide13.subtitle": {"message": "迈向通用人工智能的征程"}, "ai101.slide14.title": {"message": "AI威胁：担心还是拥抱？"}, "ai101.slide14.subtitle": {"message": "理性看待AI风险"}, "ai101.slide15.title": {"message": "AI时代的生存之道"}, "ai101.slide15.subtitle": {"message": "适应变化，拥抱未来"}, "ai101.slide16.title": {"message": "AI时代的核心技能"}, "ai101.slide16.subtitle": {"message": "培养与AI协作互补并增强人类能力的关键素养"}, "ai101.slide16.criticalThinking.title": {"message": "批判性思维"}, "ai101.slide16.criticalThinking.content": {"message": "具备分析、评估与综合信息的能力"}, "ai101.slide16.creativity.title": {"message": "创造力"}, "ai101.slide16.creativity.content": {"message": "能够提出新颖想法和创新解决方案"}, "ai101.slide16.emotionalIntelligence.title": {"message": "情绪智能"}, "ai101.slide16.emotionalIntelligence.content": {"message": "理解并管理人际互动中的情绪"}, "ai101.slide16.aiLiteracy.title": {"message": "AI素养"}, "ai101.slide16.aiLiteracy.content": {"message": "理解AI的能力、局限性及其伦理影响"}, "ai101.slide16.systemsThinking.title": {"message": "协作能力"}, "ai101.slide16.systemsThinking.content": {"message": "能与人类及AI系统高效协作，发挥团队优势"}, "ai101.slide16.adaptability.title": {"message": "适应性学习"}, "ai101.slide16.adaptability.content": {"message": "快速学习新技能并适应变化环境的能力"}, "ai101.slide17.title": {"message": "AI协作的艺术"}, "ai101.slide17.subtitle": {"message": "短期、中期和长期战略规划"}, "ai101.slide17.shortTerm.title": {"message": "短期（1-2年）"}, "ai101.slide17.shortTerm.item1": {"message": "学习有效使用AI工具"}, "ai101.slide17.shortTerm.item2": {"message": "了解AI的能力和局限性"}, "ai101.slide17.shortTerm.item3": {"message": "培养提示工程技能"}, "ai101.slide17.mediumTerm.title": {"message": "中期（3-5年）"}, "ai101.slide17.mediumTerm.item1": {"message": "掌握人机协作工作流程"}, "ai101.slide17.mediumTerm.item2": {"message": "开发AI辅助教学方法"}, "ai101.slide17.mediumTerm.item3": {"message": "构建AI素养课程"}, "ai101.slide17.longTerm.title": {"message": "长期（5年以上）"}, "ai101.slide17.longTerm.item1": {"message": "引领AI增强的教育转型"}, "ai101.slide17.longTerm.item2": {"message": "关注独特的人文教育价值"}, "ai101.educationalTransformation.title": {"message": "人工智能时代更广泛的教育变革"}, "ai101.educationalTransformation.subtitle": {"message": "涵盖课程、教学法、师生角色、评估方式的系统性变革"}, "ai101.educationalTransformation.curriculum.title": {"message": "课程与教学法的重新定义"}, "ai101.educationalTransformation.curriculum.item1": {"message": "向能力本位与跨学科学习的转变"}, "ai101.educationalTransformation.curriculum.item2": {"message": "强调\"机器无法替代的技能\""}, "ai101.educationalTransformation.curriculum.item3": {"message": "AI素养的整合作为核心课程"}, "ai101.educationalTransformation.curriculum.item4": {"message": "个性化与自适应课程"}, "ai101.educationalTransformation.roles.title": {"message": "教育者与学习者角色的演进"}, "ai101.educationalTransformation.roles.item1": {"message": "教育者成为\"学习架构师\"与\"向导\""}, "ai101.educationalTransformation.roles.item2": {"message": "学习者成为主动的共同创造者与批判性消费者"}, "ai101.educationalTransformation.roles.item3": {"message": "新技能需求：AI素养、数据分析、伦理指导"}, "ai101.educationalTransformation.assessment.title": {"message": "评估方法的新路径"}, "ai101.educationalTransformation.assessment.item1": {"message": "AI驱动的形成性与总结性评估"}, "ai101.educationalTransformation.assessment.item2": {"message": "个性化与自适应评估"}, "ai101.educationalTransformation.assessment.item3": {"message": "聚焦真实性、过程性与持续性反馈"}, "ai101.educationalTransformation.assessment.item4": {"message": "对传统评估有效性的挑战"}, "ai101.educationalTransformation.humanCentered.title": {"message": "以人为本的AI教育"}, "ai101.educationalTransformation.humanCentered.content": {"message": "系统性教育范式转变：\n\n课程 → 能力本位，AI素养整合\n角色 → 教育者为架构师，学习者为共创者\n评估 → 真实性、持续性、人机协作\n目标 → 在AI世界中培养人类福祉、能动性和伦理发展"}, "ai101.emergingConcepts.title": {"message": "新兴教育理念的曙光"}, "ai101.emergingConcepts.subtitle": {"message": "人与AI共生学习的新范式"}, "ai101.emergingConcepts.colearning.title": {"message": "人-AI协同学习"}, "ai101.emergingConcepts.colearning.description": {"message": "人类与AI系统在协作中共同学习、相互适应并随时间共同进化"}, "ai101.emergingConcepts.colearning.item1": {"message": "共享心智模型和共同基础"}, "ai101.emergingConcepts.colearning.item2": {"message": "AI作为团队伙伴，而非仅仅是工具"}, "ai101.emergingConcepts.colearning.item3": {"message": "通过互动实现相互学习和适应"}, "ai101.emergingConcepts.hcaif.title": {"message": "以人为本的AI优先教育 (HCAIF)"}, "ai101.emergingConcepts.hcaif.description": {"message": "在充分利用AI能力的同时，优先考虑人类价值观、伦理应用和个性化反馈"}, "ai101.emergingConcepts.hcaif.item1": {"message": "AI增强而非取代人类能力"}, "ai101.emergingConcepts.hcaif.item2": {"message": "整合技术、用户体验和伦理考量"}, "ai101.emergingConcepts.hcaif.item3": {"message": "强调AI使用的明确说明和反思"}, "ai101.emergingConcepts.irreplaceable.title": {"message": "\"机器无法替代的技能\"哲学立场"}, "ai101.emergingConcepts.irreplaceable.description": {"message": "专注于发展AI无法复制的独特人类能力"}, "ai101.emergingConcepts.irreplaceable.item1": {"message": "批判性思维、创造力、情感智能"}, "ai101.emergingConcepts.irreplaceable.item2": {"message": "伦理判断和复杂协作能力"}, "ai101.emergingConcepts.irreplaceable.item3": {"message": "以人为中心的创新和价值驱动的成果"}, "ai101.emergingConcepts.epistemological.title": {"message": "面向\"认识论知识\"与适应性"}, "ai101.emergingConcepts.epistemological.description": {"message": "理解知识是如何构建、验证和应用于快速变化的世界"}, "ai101.emergingConcepts.epistemological.item1": {"message": "元认知技能和学习策略"}, "ai101.emergingConcepts.epistemological.item2": {"message": "适应性和终身学习能力"}, "ai101.emergingConcepts.epistemological.item3": {"message": "整合编程、数据科学、复杂系统"}, "ai101.aiParadigms.title": {"message": "新兴AI驱动教育理念的核心宗旨"}, "ai101.aiParadigms.subtitle": {"message": "新教育方法的比较分析"}, "ai101.aiParadigms.table.paradigm": {"message": "新兴理念"}, "ai101.aiParadigms.table.principles": {"message": "核心原则"}, "ai101.aiParadigms.table.humanRole": {"message": "人类学习者角色"}, "ai101.aiParadigms.table.aiRole": {"message": "AI角色"}, "ai101.aiParadigms.table.outcomes": {"message": "学习成果"}, "ai101.aiParadigms.table.colearning": {"message": "人-AI协同学习"}, "education.hero.title": {"message": "FunBlocks AI 赋能教育：开启智能学习新纪元"}, "education.hero.subtitle": {"message": "一体化人工智能工作空间，革新教与学的方式"}, "education.hero.description": {"message": "FunBlocks AI 将人工智能技术与教育学理论深度融合，为教育工作者和学习者提供强大的思维工具，培养面向未来的核心能力。"}, "education.hero.cta.primary": {"message": "免费体验"}, "education.hero.cta.secondary": {"message": "观看演示"}, "education.challenges.title": {"message": "当今教育面临的挑战"}, "education.challenges.description": {"message": "教育工作者和学习者在现代教育环境中面临着前所未有的挑战"}, "education.challenges.workload.title": {"message": "教师工作负担过重"}, "education.challenges.workload.description": {"message": "繁重的备课、批改作业和行政事务占用了大量时间，影响了与学生的直接互动"}, "education.challenges.engagement.title": {"message": "学生参与度不足"}, "education.challenges.engagement.description": {"message": "传统教学方法难以激发学生兴趣，特别是面对复杂或不感兴趣的学科"}, "education.challenges.diversity.title": {"message": "学习需求多样化"}, "education.challenges.diversity.description": {"message": "学生的学习节奏、风格和需求各不相同，需要个性化的教学方法"}, "education.challenges.skills.title": {"message": "未来技能培养"}, "education.challenges.skills.description": {"message": "学生需要培养批判性思维、创造力和AI素养等21世纪核心技能"}, "education.solution.title": {"message": "FunBlocks AI：您的一体化教育副驾驶"}, "education.solution.description": {"message": "FunBlocks AI 提供全面而强大的解决方案，系统性地提升教与学的效率和深度"}, "education.solution.workspace.title": {"message": "一体化AI工作空间"}, "education.solution.workspace.description": {"message": "集成思维导图、文档创作、演示制作和专业AI工具于一体"}, "education.solution.thinking.title": {"message": "增强人类智慧"}, "education.solution.thinking.description": {"message": "在AI时代，您的思维至关重要 - 我们致力于成为人类智慧的放大器"}, "education.solution.visual.title": {"message": "视觉优先交互"}, "education.solution.visual.description": {"message": "超越文本的体验，通过无限画布实现多维度思考和探索"}, "education.features.title": {"message": "FunBlocks AI 核心功能：释放教育潜能"}, "education.features.description": {"message": "FunBlocks AI 提供一系列强大的工具，深度融入教育的各个环节"}, "education.features.visualization.title": {"message": "知识可视化"}, "education.features.visualization.subtitle": {"message": "促进深度理解"}, "education.features.visualization.mindmap": {"message": "AI思维导图：快速生成结构清晰的思维导图"}, "education.features.visualization.infographic": {"message": "AI信息图表：将文本转化为视觉化摘要"}, "education.features.visualization.benefit": {"message": "帮助理清复杂概念，增强知识记忆和保留"}, "education.features.content.title": {"message": "内容创作"}, "education.features.content.subtitle": {"message": "精简教学准备流程"}, "education.features.content.slides": {"message": "AI演示文稿：一键生成专业的教学幻灯片"}, "education.features.content.docs": {"message": "AI文档：智能写作助手帮助创建教学材料"}, "education.features.content.benefit": {"message": "显著节省备课时间，提升教学材料质量"}, "education.features.thinking.title": {"message": "高阶思维培养"}, "education.features.thinking.subtitle": {"message": "基于教育学理论的AI工具"}, "education.features.thinking.bloom": {"message": "AI布鲁姆大脑：基于布鲁姆认知分类法的学习设计"}, "education.features.thinking.critical": {"message": "AI批判性分析：培养逻辑思维和分析能力"}, "education.features.thinking.creative": {"message": "AI头脑风暴：运用经典思维模型激发创意"}, "education.features.thinking.benefit": {"message": "培养学生的创新能力和结构化思维"}, "education.benefits.title": {"message": "为学习型社区带来的切实益处"}, "education.benefits.description": {"message": "FunBlocks AI 致力于为整个学习社区带来实实在在的价值"}, "education.benefits.educators.title": {"message": "赋能教育工作者"}, "education.benefits.educators.workload": {"message": "减轻行政与备课负担"}, "education.benefits.educators.differentiation": {"message": "促进差异化教学"}, "education.benefits.educators.engagement": {"message": "创建引人入胜的互动材料"}, "education.benefits.educators.growth": {"message": "促进专业成长"}, "education.benefits.students.title": {"message": "提升学生学习体验"}, "education.benefits.students.personalized": {"message": "实现个性化学习路径"}, "education.benefits.students.understanding": {"message": "使复杂主题易于理解"}, "education.benefits.students.engagement": {"message": "提升学习参与度和积极性"}, "education.benefits.students.skills": {"message": "培养面向未来的核心技能"}, "education.cases.title": {"message": "真实教育成功案例"}, "education.cases.description": {"message": "了解教育机构和个人如何通过FunBlocks AI改变教学和学习方式"}, "education.cases.university.title": {"message": "大学教学转型"}, "education.cases.university.description": {"message": "某大学认知心理学教授使用AIFlow辅助教学，学生对复杂概念的理解程度提升了40%"}, "education.cases.university.result1": {"message": "复杂概念理解提升40%"}, "education.cases.university.result2": {"message": "教学材料准备时间减少60%"}, "education.cases.university.result3": {"message": "学生课堂参与度显著提高"}, "education.cases.medical.title": {"message": "医学教育突破"}, "education.cases.medical.description": {"message": "医学生使用AI知识阶梯学习复杂的心血管生理学，学习效率和考试成绩显著提升"}, "education.cases.medical.result1": {"message": "学习时间减少30-40%"}, "education.cases.medical.result2": {"message": "考试成绩提高27%"}, "education.cases.medical.result3": {"message": "知识保留率大幅提升"}, "education.cases.k12.title": {"message": "K-12创新教学"}, "education.cases.k12.description": {"message": "中小学教师使用FunBlocks AI教育工具创建互动式学习材料，显著提升学生理解和参与度"}, "education.cases.k12.result1": {"message": "学生参与度提升50%"}, "education.cases.k12.result2": {"message": "批判性思维能力提高35%"}, "education.cases.k12.result3": {"message": "创造性问题解决能力增强"}, "education.integration.title": {"message": "无缝集成：融入您的教育环境"}, "education.integration.description": {"message": "FunBlocks AI 设计注重易用性、可及性、安全性以及与现有生态系统的兼容性"}, "education.integration.accessibility.title": {"message": "用户友好与可访问性"}, "education.integration.accessibility.free": {"message": "免费试用：新用户可免费体验核心功能"}, "education.integration.accessibility.api": {"message": "自带API密钥：使用自己的AI模型API，降低使用成本"}, "education.integration.accessibility.subscription": {"message": "灵活订阅：一次订阅，所有模型"}, "education.integration.security.title": {"message": "数据隐私和安全"}, "education.integration.security.encryption": {"message": "SSL加密和HTTPS协议保护数据传输"}, "education.integration.security.access": {"message": "严格的访问控制机制"}, "education.integration.security.compliance": {"message": "遵循数据保护原则和最佳实践"}, "education.future.title": {"message": "规划未来：FunBlocks AI 与教育的演进"}, "education.future.description": {"message": "FunBlocks AI 致力于塑造人机协作的教育未来"}, "education.future.collaboration.title": {"message": "促进学习中的人机协作"}, "education.future.collaboration.description": {"message": "AI作为增强人类智慧的伙伴，而非简单的替代者，共同探索和创造"}, "education.future.adaptation.title": {"message": "助力学生适应AI融合的世界"}, "education.future.adaptation.description": {"message": "培养学生的AI素养、批判性思维和创造力等21世纪核心技能"}, "education.future.evolution.title": {"message": "教育者角色的演变"}, "education.future.evolution.description": {"message": "从知识传授者转变为学习引导者和AI辅助体验的策划者"}, "education.cta.title": {"message": "携手 FunBlocks AI，革新您的教与学"}, "education.cta.description": {"message": "选择 FunBlocks AI，不仅仅是选择一款先进的技术工具，更是选择拥抱一种更智能、更高效、更以人为本的教育未来"}, "education.cta.primary": {"message": "立即开始免费体验"}, "education.cta.secondary": {"message": "了解更多产品功能"}, "education.cta.features": {"message": "✓ 免费试用所有核心功能\n✓ 无需信用卡\n✓ 即刻开始使用"}, "education.faq.title": {"message": "常见问题解答"}, "education.faq.description": {"message": "获取关于FunBlocks AI在教育领域应用的常见问题解答"}, "education.faq.q1.question": {"message": "什么是FunBlocks AI，它如何帮助教育？"}, "education.faq.q1.answer": {"message": "FunBlocks AI是一个一体化AI工作空间，将人工智能技术与教育理论深度融合。它提供强大的思维工具，包括AI思维导图、演示文稿生成、批判性思维框架和视觉学习工具，帮助教育者创建引人入胜的材料，学生培养21世纪技能。"}, "education.faq.q2.question": {"message": "FunBlocks AI如何减轻教师工作负担？"}, "education.faq.q2.answer": {"message": "FunBlocks AI自动化和简化许多耗时的任务，如创建演示文稿、生成思维导图、设计信息图表，以及基于布鲁姆分类法等教育框架构建课程计划。这让教师能够更多地专注于与学生的直接互动和创造性教学方法。"}, "education.faq.q3.question": {"message": "FunBlocks AI支持哪些教育框架？"}, "education.faq.q3.answer": {"message": "FunBlocks AI支持各种经过验证的教育框架，包括布鲁姆分类法（BloomBrain）、马扎诺分类法（MarzanoBrain）、SOLO分类法（SOLOBrain）和知识深度（DOKBrain）。这些工具帮助根据既定的教学原则构建学习体验。"}, "education.faq.q4.question": {"message": "FunBlocks AI适用于所有教育层次吗？"}, "education.faq.q4.answer": {"message": "是的，FunBlocks AI旨在支持各个层次的教育，从K-12到高等教育和专业发展。这些工具可以适应不同的复杂程度和学习需求，使其在各种教育环境中都很实用。"}, "education.faq.q5.question": {"message": "FunBlocks AI如何确保数据隐私和安全？"}, "education.faq.q5.answer": {"message": "FunBlocks AI通过SSL加密、HTTPS协议、严格的访问控制机制和遵循数据保护原则来优先保护数据安全。该平台还提供\"自带API密钥\"选项，让用户控制自己的数据和AI模型使用。"}, "education.faq.q6.question": {"message": "我可以免费试用FunBlocks AI吗？"}, "education.faq.q6.answer": {"message": "是的，FunBlocks AI提供免费试用，允许新用户体验所有核心功能，无需信用卡。此外，用户可以自带API密钥以降低成本访问高级功能，这对预算有限的教育机构来说很有吸引力。"}, "education.aiEra.title": {"message": "AI时代教育：挑战与机遇并存"}, "education.aiEra.description": {"message": "AI时代为教育带来了前所未有的挑战和变革性机遇"}, "education.aiEra.challenges.subtitle": {"message": "关键挑战"}, "education.aiEra.workload.title": {"message": "教学负担与效率平衡"}, "education.aiEra.workload.description": {"message": "如何在减轻教师工作负担的同时，保持高质量的教学效果和个性化关注"}, "education.aiEra.collaboration.title": {"message": "人机协作模式探索"}, "education.aiEra.collaboration.description": {"message": "建立有效的人类-AI协作关系，发挥各自优势，实现教育效果最大化"}, "education.aiEra.thinking.title": {"message": "高阶思维能力培养"}, "education.aiEra.thinking.description": {"message": "在AI辅助环境下，如何培养学生的批判性思维、创造力和复杂问题解决能力"}, "education.aiEra.transformation.title": {"message": "教育模式变革"}, "education.aiEra.transformation.description": {"message": "适应AI时代需求，重新定义教学方法、评估体系和学习目标"}, "education.aiEra.opportunities.subtitle": {"message": "变革性机遇"}, "education.aiEra.opportunities.personalized.title": {"message": "个性化学习革命"}, "education.aiEra.opportunities.personalized.description": {"message": "AI技术使大规模个性化教育成为可能，为每个学生提供定制化学习体验"}, "education.aiEra.opportunities.efficiency.title": {"message": "教学效率提升"}, "education.aiEra.opportunities.efficiency.description": {"message": "自动化重复性任务，让教育者专注于创造性教学和学生互动"}, "education.aiEra.opportunities.insights.title": {"message": "数据驱动洞察"}, "education.aiEra.opportunities.insights.description": {"message": "通过学习数据分析，深入了解学生需求，优化教学策略"}, "education.collaboration.title": {"message": "人机协作：教育的未来"}, "education.collaboration.description": {"message": "FunBlocks AI倡导以人为中心的方法，让AI增强人类智慧而非取代人类"}, "education.collaboration.principles.subtitle": {"message": "人机协作核心原则"}, "education.collaboration.partnership.title": {"message": "AI作为思维伙伴"}, "education.collaboration.partnership.description": {"message": "AI不是替代者，而是增强人类认知能力的智能伙伴，共同探索和创造"}, "education.collaboration.augmentation.title": {"message": "能力增强而非替代"}, "education.collaboration.augmentation.description": {"message": "AI技术放大人类的创造力、批判性思维和问题解决能力"}, "education.collaboration.balance.title": {"message": "人机优势互补"}, "education.collaboration.balance.description": {"message": "发挥人类的情感智能、创造力和AI的计算能力、数据处理优势"}, "education.solution.subtitle": {"message": "FunBlocks AI：您的一体化教育副驾驶"}, "education.thinking.title": {"message": "AI时代的高阶思维"}, "education.thinking.description": {"message": "培养批判性思维、创造力和复杂问题解决能力，与AI能力形成互补"}, "education.thinking.definition.title": {"message": "什么是高阶思维？"}, "education.thinking.definition.text": {"message": "高阶思维涉及超越基本记忆和理解的复杂认知过程。它包括分析、综合、评估和创造——这些技能使学生能够批判性思考、解决复杂问题并产生创新解决方案。"}, "education.thinking.analysis.title": {"message": "分析思维"}, "education.thinking.analysis.description": {"message": "分解复杂信息，识别模式、关系和因果联系"}, "education.thinking.analysis.tools": {"message": "AI逻辑透镜、AI批判性分析、AI思维导图"}, "education.thinking.synthesis.title": {"message": "综合思维"}, "education.thinking.synthesis.description": {"message": "整合不同信息源，形成新的理解和见解"}, "education.thinking.synthesis.tools": {"message": "AIFlow无限画布、AI头脑风暴、AI心智套件"}, "education.thinking.evaluation.title": {"message": "评估思维"}, "education.thinking.evaluation.description": {"message": "判断信息的价值、可信度和相关性，做出明智决策"}, "education.thinking.evaluation.tools": {"message": "AI反思教练、AI问题建构、AI批判性分析"}, "education.thinking.creation.title": {"message": "创造思维"}, "education.thinking.creation.description": {"message": "生成原创想法、解决方案和创新成果"}, "education.thinking.creation.tools": {"message": "AI头脑风暴、AI信息图表、AI幻灯片生成"}, "education.thinking.tools.label": {"message": "FunBlocks AI工具："}, "education.features.subtitle": {"message": "核心功能：释放教育潜能"}, "education.skills.title": {"message": "AI时代必备素质"}, "education.skills.description": {"message": "培养与AI协作互补的人类核心能力"}, "education.skills.critical.title": {"message": "批判性思维"}, "education.skills.critical.description": {"message": "分析、评估和综合信息的能力，质疑假设，识别偏见和逻辑谬误"}, "education.skills.creative.title": {"message": "创造性思维"}, "education.skills.creative.description": {"message": "产生新颖想法、创新解决方案和原创内容的能力"}, "education.skills.emotional.title": {"message": "情感智能"}, "education.skills.emotional.description": {"message": "理解和管理情绪，建立有效人际关系的能力"}, "education.skills.collaboration.title": {"message": "协作能力"}, "education.skills.collaboration.description": {"message": "与人类和AI系统有效协作，发挥团队优势的能力"}, "education.skills.adaptability.title": {"message": "适应性学习"}, "education.skills.adaptability.description": {"message": "快速学习新技能，适应变化环境的能力"}, "education.skills.aiLiteracy.title": {"message": "AI素养"}, "education.skills.aiLiteracy.description": {"message": "理解AI能力、局限性和伦理影响，有效使用AI工具的能力"}, "education.skills.category.cognitive": {"message": "认知技能"}, "education.skills.category.social": {"message": "社交技能"}, "education.skills.category.meta": {"message": "元技能"}, "education.skills.help.title": {"message": "FunBlocks AI如何培养这些技能"}, "education.skills.help.practice.title": {"message": "结构化练习"}, "education.skills.help.practice.description": {"message": "AI布鲁姆大脑和批判性分析等AI工具提供结构化框架来培养思维技能"}, "education.skills.help.collaboration.title": {"message": "AI协作体验"}, "education.skills.help.collaboration.description": {"message": "学生学会与AI作为思维伙伴合作，培养必要的协作技能"}, "education.skills.help.creativity.title": {"message": "创意表达"}, "education.skills.help.creativity.description": {"message": "视觉工具和头脑风暴功能鼓励创造性思维和创新问题解决"}, "education.future.subtitle": {"message": "教育的未来"}, "education.future.hero.title": {"message": "FunBlocks AI：塑造教育的未来"}, "education.future.hero.subtitle": {"message": "人机协作，开启下一代学习新纪元"}, "education.future.hero.description": {"message": "在AI时代，教育面临前所未有的变革。FunBlocks AI为教育工作者和学习者提供先进的思维工具，培养面向未来的核心技能，促进增强而非替代人类智慧的人机协作。"}, "education.future.hero.cta.primary": {"message": "探索解决方案"}, "education.future.hero.cta.secondary": {"message": "观看AI演示"}, "education.future.impact.title": {"message": "AI革命：改变我们的世界"}, "education.future.impact.description": {"message": "人工智能正在重塑人类生活的方方面面，创造前所未有的机遇和根本性挑战，需要教育和学习的新方法。"}, "education.future.impact.society.title": {"message": "社会变革"}, "education.future.impact.society.description": {"message": "AI正在重新定义工作、经济和社会结构，要求我们重新思考教育的目标和方法。"}, "education.future.impact.individual.title": {"message": "个人发展"}, "education.future.impact.individual.description": {"message": "每个人都需要发展新的技能和思维方式，以在AI增强的世界中茁壮成长。"}, "education.future.impact.workplace.title": {"message": "工作场所转型"}, "education.future.impact.workplace.description": {"message": "未来的工作将更多地依赖人机协作，需要新的技能组合和工作方式。"}, "education.future.impact.education.title": {"message": "教育系统重构"}, "education.future.impact.education.description": {"message": "传统教育模式必须适应AI时代的需求，培养学生的批判性思维和创新能力。"}, "education.future.impact.insight.title": {"message": "问题不在于AI是否会改变教育"}, "education.future.impact.insight.text": {"message": "问题在于：我们如何准备学习者在AI增强的世界中茁壮成长，同时保持和增强独特的人类能力？"}, "education.future.collaboration.main.title": {"message": "人机协作：未来教育的核心"}, "education.future.collaboration.main.description": {"message": "教育的未来不在于用AI替代人类，而在于创造强大的伙伴关系，让AI放大人类的智慧、创造力和智慧。"}, "education.future.collaboration.philosophy.title": {"message": "我们的理念：以人为本的AI"}, "education.future.collaboration.philosophy.text": {"message": "AI应该增强人类能力，而不是替代它们。在教育中，这意味着创造帮助教师更好地教学、学生更深入学习的工具，同时保持同理心、创造力和批判性思维等不可替代的人类元素。"}, "education.future.collaboration.partnership.title": {"message": "AI作为思维伙伴"}, "education.future.collaboration.partnership.description": {"message": "AI不是替代者，而是增强人类认知能力的智能伙伴，共同探索和创造。"}, "education.future.collaboration.augmentation.title": {"message": "增强而非替代"}, "education.future.collaboration.augmentation.description": {"message": "AI技术放大人类的创造力、批判性思维和问题解决能力，而不是取代这些能力。"}, "education.future.collaboration.balance.title": {"message": "人机优势互补"}, "education.future.collaboration.balance.description": {"message": "发挥人类的情感智能和创造力与AI的计算能力和数据处理优势。"}, "education.future.collaboration.benefits.title": {"message": "为什么人机协作有效"}, "education.future.collaboration.benefits.efficiency.title": {"message": "提升效率"}, "education.future.collaboration.benefits.efficiency.description": {"message": "AI处理重复性任务，让人类专注于创造性和战略性工作。"}, "education.future.collaboration.benefits.creativity.title": {"message": "激发创造力"}, "education.future.collaboration.benefits.creativity.description": {"message": "AI提供新的视角和可能性，激发人类的创新思维。"}, "education.future.collaboration.benefits.growth.title": {"message": "促进成长"}, "education.future.collaboration.benefits.growth.description": {"message": "人机协作创造持续学习和技能发展的机会。"}, "education.future.skills.title": {"message": "AI时代必备的人类素质"}, "education.future.skills.description": {"message": "随着AI改变我们的世界，某些独特的人类能力变得比以往任何时候都更有价值。这些技能补充AI的优势，确保人类仍然是有意义工作和学习的中心。"}, "education.future.skills.cognitive.category": {"message": "认知技能"}, "education.future.skills.social.category": {"message": "社交技能"}, "education.future.skills.meta.category": {"message": "元技能"}, "education.future.skills.critical.title": {"message": "批判性思维"}, "education.future.skills.critical.description": {"message": "分析、评估和综合信息，质疑假设，识别偏见和逻辑谬误的能力。"}, "education.future.skills.creative.title": {"message": "创造性思维"}, "education.future.skills.creative.description": {"message": "产生新颖想法、创新解决方案和原创内容的能力。"}, "education.future.skills.analytical.title": {"message": "分析思维"}, "education.future.skills.analytical.description": {"message": "分解复杂问题，识别模式和关系，做出基于数据的决策的能力。"}, "education.future.skills.emotional.title": {"message": "情感智能"}, "education.future.skills.emotional.description": {"message": "理解和管理情绪，建立有效人际关系的能力。"}, "education.future.skills.collaboration.title": {"message": "协作能力"}, "education.future.skills.collaboration.description": {"message": "与人类和AI系统有效协作，发挥团队优势的能力。"}, "education.future.skills.communication.title": {"message": "沟通能力"}, "education.future.skills.communication.description": {"message": "清晰表达想法，有效倾听，跨文化和跨媒体沟通的能力。"}, "education.future.skills.adaptability.title": {"message": "适应性学习"}, "education.future.skills.adaptability.description": {"message": "快速学习新技能，适应变化环境的能力。"}, "education.future.skills.aiLiteracy.title": {"message": "AI素养"}, "education.future.skills.aiLiteracy.description": {"message": "理解AI能力、局限性和伦理影响，有效使用AI工具的能力。"}, "education.future.skills.learning.title": {"message": "终身学习"}, "education.future.skills.learning.description": {"message": "持续学习和自我发展，保持好奇心和成长心态的能力。"}, "education.future.skills.insight.title": {"message": "AI世界中的人类优势"}, "education.future.skills.insight.text": {"message": "虽然AI擅长处理信息和模式识别，但人类带来不可替代的品质：同理心、伦理推理、创造性问题解决以及寻找意义和目标的能力。未来属于那些能够有效结合这些人类优势与AI能力的人。"}, "education.future.thinking.title": {"message": "高阶思维：未来学习的基础"}, "education.future.thinking.description": {"message": "在AI能够瞬间处理信息的时代，批判性、创造性和分析性思维能力成为人类最大的资产。高阶思维技能是区分人类智能与人工智能的关键。"}, "education.future.thinking.definition.title": {"message": "什么使思维成为「高阶」？"}, "education.future.thinking.definition.text": {"message": "高阶思维超越记忆和基本理解。它涉及分析复杂信息、综合多个来源的想法、评估证据和论证，以及创造原创解决方案。这些认知过程对于在AI增强的世界中导航至关重要。"}, "education.future.thinking.framework.title": {"message": "高阶思维的四大支柱"}, "education.future.thinking.analysis.title": {"message": "分析思维"}, "education.future.thinking.analysis.description": {"message": "分解复杂信息以识别模式、关系和因果联系。"}, "education.future.thinking.analysis.example": {"message": "分析一篇研究论文的方法论和结论的有效性。"}, "education.future.thinking.synthesis.title": {"message": "综合思维"}, "education.future.thinking.synthesis.description": {"message": "整合不同信息源以形成新的理解和见解。"}, "education.future.thinking.synthesis.example": {"message": "结合历史、经济和社会学观点来理解当前事件。"}, "education.future.thinking.evaluation.title": {"message": "评估思维"}, "education.future.thinking.evaluation.description": {"message": "判断信息的价值、可信度和相关性以做出明智决策。"}, "education.future.thinking.evaluation.example": {"message": "评估不同新闻来源的可信度和偏见。"}, "education.future.thinking.creation.title": {"message": "创造思维"}, "education.future.thinking.creation.description": {"message": "生成原创想法、解决方案和创新成果。"}, "education.future.thinking.creation.example": {"message": "设计解决环境问题的创新方案。"}, "education.future.thinking.example.label": {"message": "示例："}, "education.future.thinking.importance.title": {"message": "为什么高阶思维比以往任何时候都重要"}, "education.future.thinking.importance.innovation.title": {"message": "推动创新"}, "education.future.thinking.importance.innovation.description": {"message": "创造性和批判性思维导致AI无法单独产生的突破性解决方案。"}, "education.future.thinking.importance.resilience.title": {"message": "建立韧性"}, "education.future.thinking.importance.resilience.description": {"message": "分析技能帮助应对不确定性并适应快速的技术变化。"}, "education.future.thinking.importance.purpose.title": {"message": "提供目标"}, "education.future.thinking.importance.purpose.description": {"message": "评估性思维帮助确定什么最重要，指导伦理决策。"}, "education.future.solutions.title": {"message": "FunBlocks AI：赋能高阶思维"}, "education.future.solutions.description": {"message": "我们的综合AI驱动平台专门设计用于增强人类思维能力，而不是替代它们。每个工具都以教育理论和认知科学原理为核心构建。"}, "education.future.solutions.aiflow.title": {"message": "AIFlow 思维导图"}, "education.future.solutions.aiflow.description": {"message": "可视化思维工具，帮助分解复杂概念，建立知识连接，促进深度理解。"}, "education.future.solutions.aiflow.feature1": {"message": "AI辅助的概念映射和关系分析"}, "education.future.solutions.aiflow.feature2": {"message": "基于教育框架的结构化思维指导"}, "education.future.solutions.aiflow.feature3": {"message": "协作式学习和知识共享平台"}, "education.future.solutions.workspace.title": {"message": "一体化AI工作空间"}, "education.future.solutions.workspace.description": {"message": "集成文档、演示、信息图表创建的完整学习和教学环境。"}, "education.future.solutions.workspace.feature1": {"message": "AI驱动的内容创建和编辑工具"}, "education.future.solutions.workspace.feature2": {"message": "多媒体学习资源整合平台"}, "education.future.solutions.workspace.feature3": {"message": "个性化学习路径和进度跟踪"}, "education.future.solutions.tools.title": {"message": "专业教育AI工具"}, "education.future.solutions.tools.description": {"message": "基于布鲁姆分类法等教育理论的专业AI工具套件。"}, "education.future.solutions.tools.feature1": {"message": "BloomBrain、MarzanoBrain等认知框架工具"}, "education.future.solutions.tools.feature2": {"message": "批判性思维和创造性思维训练工具"}, "education.future.solutions.tools.feature3": {"message": "评估和反思支持工具"}, "education.future.solutions.explore": {"message": "探索 →"}, "education.future.solutions.benefits.title": {"message": "对学习成果的验证影响"}, "education.future.solutions.benefits.efficiency.title": {"message": "教学效率提升"}, "education.future.solutions.benefits.efficiency.description": {"message": "减少备课时间，提高教学质量"}, "education.future.solutions.benefits.engagement.title": {"message": "学习参与度提升"}, "education.future.solutions.benefits.engagement.description": {"message": "通过视觉化和互动式学习提高学生参与度"}, "education.future.solutions.benefits.thinking.title": {"message": "思维能力提升"}, "education.future.solutions.benefits.thinking.description": {"message": "显著提高批判性思维和问题解决能力"}, "education.future.solutions.howItWorks.title": {"message": "FunBlocks AI如何增强高阶思维"}, "education.future.solutions.workflow.step1.title": {"message": "结构化框架"}, "education.future.solutions.workflow.step1.description": {"message": "内置的教育框架如布鲁姆分类法指导思维过程"}, "education.future.solutions.workflow.step2.title": {"message": "AI辅助探索"}, "education.future.solutions.workflow.step2.description": {"message": "AI帮助分解复杂主题并建议新的视角"}, "education.future.solutions.workflow.step3.title": {"message": "视觉综合"}, "education.future.solutions.workflow.step3.description": {"message": "将想法转化为增强理解和记忆的视觉格式"}, "education.future.solutions.workflow.step4.title": {"message": "反思实践"}, "education.future.solutions.workflow.step4.description": {"message": "内置反思工具帮助巩固学习并发展元认知"}, "education.future.transformation.title": {"message": "伟大的教育变革"}, "education.future.transformation.description": {"message": "AI不仅仅是改变我们如何教学和学习——它正在从根本上改变教育的意义。这种变革触及教育生态系统的每个方面。"}, "education.future.transformation.curriculum.title": {"message": "课程重构"}, "education.future.transformation.curriculum.description": {"message": "从知识传授转向能力培养，强调跨学科学习和实际应用。"}, "education.future.transformation.curriculum.change1": {"message": "能力本位学习替代知识记忆"}, "education.future.transformation.curriculum.change2": {"message": "跨学科整合和项目式学习"}, "education.future.transformation.curriculum.change3": {"message": "AI素养成为核心课程内容"}, "education.future.transformation.pedagogy.title": {"message": "教学法革新"}, "education.future.transformation.pedagogy.description": {"message": "从传统讲授转向引导式学习，强调学生主动参与和批判性思维。"}, "education.future.transformation.pedagogy.change1": {"message": "从教师中心转向学生中心"}, "education.future.transformation.pedagogy.change2": {"message": "个性化和自适应学习路径"}, "education.future.transformation.pedagogy.change3": {"message": "人机协作的教学模式"}, "education.future.transformation.assessment.title": {"message": "评估方式变革"}, "education.future.transformation.assessment.description": {"message": "从标准化测试转向多元化、过程性和真实性评估。"}, "education.future.transformation.assessment.change1": {"message": "持续性和形成性评估"}, "education.future.transformation.assessment.change2": {"message": "项目和作品集评估"}, "education.future.transformation.assessment.change3": {"message": "AI辅助的个性化反馈"}, "education.future.transformation.roles.title": {"message": "角色重新定义"}, "education.future.transformation.roles.description": {"message": "教师和学生的角色都在发生根本性变化，适应AI时代的新需求。"}, "education.future.transformation.roles.change1": {"message": "教师成为学习促进者和导师"}, "education.future.transformation.roles.change2": {"message": "学生成为主动的知识建构者"}, "education.future.transformation.roles.change3": {"message": "AI成为智能学习伙伴"}, "education.future.transformation.vision.title": {"message": "我们对教育未来的愿景"}, "education.future.transformation.vision.text": {"message": "我们设想一个教育未来，AI放大人类潜能，每个学习者都能获得个性化、引人入胜和有意义的学习体验，教育工作者被赋权专注于他们最擅长的事情：启发、指导和培养下一代思想家和创新者。"}, "education.future.cta.title": {"message": "准备好塑造教育的未来了吗？"}, "education.future.cta.description": {"message": "加入数千名已经在使用FunBlocks AI增强思维能力并为未来做准备的教育工作者和学习者。今天就开始您的更有效、更引人入胜、更有意义的教育之旅。"}, "education.future.cta.primary": {"message": "开始免费试用"}, "education.future.cta.secondary": {"message": "观看演示"}, "education.future.cta.feature1": {"message": "免费使用所有核心功能"}, "education.future.cta.feature2": {"message": "无需信用卡"}, "education.future.cta.feature3": {"message": "立即开始使用"}, "education.future.thinking.bloom.title": {"message": "布鲁姆认知分类金字塔"}, "education.future.thinking.bloom.create": {"message": "创造"}, "education.future.thinking.bloom.create.desc": {"message": "设计、构建、规划"}, "education.future.thinking.bloom.evaluate": {"message": "评估"}, "education.future.thinking.bloom.evaluate.desc": {"message": "判断、批评、评价"}, "education.future.thinking.bloom.analyze": {"message": "分析"}, "education.future.thinking.bloom.analyze.desc": {"message": "比较、对比、检查"}, "education.future.thinking.bloom.apply": {"message": "应用"}, "education.future.thinking.bloom.apply.desc": {"message": "使用、执行、实施"}, "education.future.thinking.bloom.understand": {"message": "理解"}, "education.future.thinking.bloom.understand.desc": {"message": "解释、诠释、总结"}, "education.future.thinking.bloom.remember": {"message": "记忆"}, "education.future.thinking.bloom.remember.desc": {"message": "回忆、识别、列举"}, "education.future.thinking.bloom.subtitle": {"message": "认知复杂性从底部到顶部递增"}, "ai101.aiParadigms.table.colearning.principles": {"message": "相互学习、适应、共享心智模型"}, "ai101.aiParadigms.table.colearning.human": {"message": "协作伙伴、主动参与者、适应者"}, "ai101.aiParadigms.table.colearning.ai": {"message": "学习伙伴、团队成员、适应性智能体"}, "ai101.aiParadigms.table.colearning.outcomes": {"message": "提升团队整体表现，发展人与AI的协同能力"}, "ai101.aiParadigms.table.hcaif": {"message": "以人为本的AI优先教育"}, "ai101.aiParadigms.table.hcaif.principles": {"message": "人类价值观优先、伦理应用、个性化"}, "ai101.aiParadigms.table.hcaif.human": {"message": "自主学习者、伦理实践者、问题解决者"}, "ai101.aiParadigms.table.hcaif.ai": {"message": "学习助手、内容生成器、反馈提供者"}, "ai101.aiParadigms.table.hcaif.outcomes": {"message": "批判性思维、伦理意识、现实世界问题解决"}, "ai101.aiParadigms.table.irreplaceable": {"message": "机器无法替代的技能"}, "ai101.aiParadigms.table.irreplaceable.principles": {"message": "人类独有能力、创造力、伦理判断"}, "ai101.aiParadigms.table.irreplaceable.human": {"message": "核心人类素养的积极发展者"}, "ai101.aiParadigms.table.irreplaceable.ai": {"message": "常规任务助手、高阶思维催化剂"}, "ai101.aiParadigms.table.irreplaceable.outcomes": {"message": "核心人类素养、AI时代竞争优势"}, "ai101.aiParadigms.table.epistemological": {"message": "认识论知识与适应性"}, "ai101.aiParadigms.table.epistemological.principles": {"message": "知识构建理解、适应性、终身学习"}, "ai101.aiParadigms.table.epistemological.human": {"message": "主动的知识探究者、终身学习者、变化适应者"}, "ai101.aiParadigms.table.epistemological.ai": {"message": "信息助手、学习路径规划者、分析支持"}, "ai101.aiParadigms.table.epistemological.outcomes": {"message": "深刻的知识理解、持续学习能力、内在动机"}, "ai101.aiParadigms.synthesis.title": {"message": "范式综合"}, "ai101.aiParadigms.synthesis.content": {"message": "这些范式相互关联，代表着整体性转变：\n\n• 人-AI协作智能而非替代\n• 人类和AI的能动性重新概念化\n• 人-AI学习关系的伦理框架\n• 关注\"为什么\"（目的、伦理、价值观）作为人类贡献\n• 哲学和伦理推理在所有领域的提升"}, "ai101.endToEnd.title": {"message": "端到端学习范式"}, "ai101.endToEnd.subtitle": {"message": "End-to-End Learning Paradigm"}, "ai101.endToEnd.concept.title": {"message": "核心概念"}, "ai101.endToEnd.concept.definition": {"message": "端到端学习是一种深度学习方法，通过单一神经网络模型直接从原始输入映射到最终输出，无需人工设计中间特征提取步骤。"}, "ai101.endToEnd.concept.coreIdea": {"message": "核心思想：让模型自主学习从输入到输出的最优映射关系"}, "ai101.endToEnd.features.title": {"message": "主要特征"}, "ai101.endToEnd.features.directMapping": {"message": "直接映射：原始数据 → 深度神经网络 → 最终结果"}, "ai101.endToEnd.features.globalOptimization": {"message": "整体优化：全局联合训练，避免子模块局部最优"}, "ai101.endToEnd.features.autoFeature": {"message": "自动特征学习：无需人工特征工程，模型自主学习表示"}, "ai101.endToEnd.features.taskDriven": {"message": "任务驱动：以最终目标为导向的优化策略"}, "ai101.endToEnd.advantages.title": {"message": "技术优势"}, "ai101.endToEnd.advantages.advantage": {"message": "优势"}, "ai101.endToEnd.advantages.description": {"message": "说明"}, "ai101.endToEnd.advantages.simplified": {"message": "简化架构"}, "ai101.endToEnd.advantages.simplifiedDesc": {"message": "减少人工设计，统一训练推理流程"}, "ai101.endToEnd.advantages.performance": {"message": "性能提升"}, "ai101.endToEnd.advantages.performanceDesc": {"message": "多领域达到SOTA，避免误差累积"}, "ai101.endToEnd.advantages.adaptability": {"message": "自适应性"}, "ai101.endToEnd.advantages.adaptabilityDesc": {"message": "自动发现任务相关特征"}, "ai101.endToEnd.advantages.optimization": {"message": "端到端优化"}, "ai101.endToEnd.advantages.optimizationDesc": {"message": "全局最优化，梯度直接传播"}, "ai101.quadrant.title": {"message": "人-AI互动四象限模型"}, "ai101.quadrant.subtitle": {"message": "AI(知道，不知道) × 人（知道，不知道）四象限互动模式"}, "ai101.quadrant.model.title": {"message": "四象限互动模型"}, "ai101.quadrant.human.know": {"message": "人类知道"}, "ai101.quadrant.human.dontKnow": {"message": "人类不知道"}, "ai101.quadrant.ai.know": {"message": "AI知道"}, "ai101.quadrant.ai.dontKnow": {"message": "AI不知道"}, "ai101.quadrant.q1.title": {"message": "任务分配与验证"}, "ai101.quadrant.q1.desc": {"message": "AI 协助完成任务，人类对结果进行验证，确保其准确性与可靠性。"}, "ai101.quadrant.q2.title": {"message": "提问与发现"}, "ai101.quadrant.q2.desc": {"message": "AI 回答问题并传授新知识，帮助人类拓展理解。"}, "ai101.quadrant.q3.title": {"message": "教学与训练"}, "ai101.quadrant.q3.desc": {"message": "人类向 AI 传授背景与领域知识，例如通过 SFT（监督微调）或 RAG（检索增强生成）等方式。"}, "ai101.quadrant.q4.title": {"message": "探索与创新"}, "ai101.quadrant.q4.desc": {"message": "人类与 AI 协作，共同探索未知领域，开展发现与创造。"}, "ai101.quadrant.dynamics.title": {"message": "动态互动原则"}, "ai101.quadrant.dynamics.item1": {"message": "知识边界是流动的，依赖于上下文"}, "ai101.quadrant.dynamics.item2": {"message": "有效协作需要理解各方优势"}, "ai101.quadrant.dynamics.item3": {"message": "目标是互补智能，而非替代"}, "ai101.quadrant.dynamics.item4": {"message": "人类和AI的持续学习与适应"}, "ai101.higherThinking.title": {"message": "AI时代的高阶思维"}, "ai101.higherThinking.subtitle": {"message": "从信息回忆到深度智力参与"}, "ai101.higherThinking.definition.title": {"message": "什么是高阶思维？"}, "ai101.higherThinking.definition.content": {"message": "高阶思维技能包含复杂的认知过程，区别于单纯的信息回忆，涉及深度智力参与、创造力和批判性分析。这些包括分析、评估和创造——需要批判性检查信息、基于标准做出判断，并将知识综合成新颖配置的认知操作。"}, "ai101.higherThinking.analysis.title": {"message": "分析"}, "ai101.higherThinking.analysis.content": {"message": "将复杂信息分解为组成部分并理解关系"}, "ai101.higherThinking.evaluation.title": {"message": "评估"}, "ai101.higherThinking.evaluation.content": {"message": "基于标准和准则做出判断，评估质量和有效性"}, "ai101.higherThinking.creation.title": {"message": "创造"}, "ai101.higherThinking.creation.content": {"message": "将知识综合成新颖配置并产生原创想法"}, "ai101.higherThinking.aiEra.title": {"message": "AI时代的重新概念化"}, "ai101.higherThinking.aiEra.item1": {"message": "批判性评估AI生成内容的能力"}, "ai101.higherThinking.aiEra.item2": {"message": "理解算法局限性和偏见"}, "ai101.higherThinking.aiEra.item3": {"message": "在技术中介环境中保持人类主体性"}, "ai101.higherThinking.aiEra.item4": {"message": "对人-AI认知互动的元认知意识"}, "ai101.cognitiveOffloading.title": {"message": "认知卸载现象"}, "ai101.cognitiveOffloading.subtitle": {"message": "AI如何改变人类思维过程"}, "ai101.cognitiveOffloading.definition.title": {"message": "什么是认知卸载？"}, "ai101.cognitiveOffloading.definition.content": {"message": "认知卸载代表使用外部工具或资源来减少心理努力并增强认知表现的实践。这种现象包括将记忆存储、计算以及越来越多的复杂推理过程委托给AI系统。"}, "ai101.cognitiveOffloading.traditional.title": {"message": "传统卸载"}, "ai101.cognitiveOffloading.traditional.item1": {"message": "笔记和外部记忆"}, "ai101.cognitiveOffloading.traditional.item2": {"message": "计算器进行计算"}, "ai101.cognitiveOffloading.traditional.item3": {"message": "地图导航"}, "ai101.cognitiveOffloading.traditional.item4": {"message": "保留高阶思维"}, "ai101.cognitiveOffloading.ai.title": {"message": "AI时代卸载"}, "ai101.cognitiveOffloading.ai.item1": {"message": "复杂分析和综合"}, "ai101.cognitiveOffloading.ai.item2": {"message": "创意内容生成"}, "ai101.cognitiveOffloading.ai.item3": {"message": "决策支持"}, "ai101.cognitiveOffloading.ai.item4": {"message": "可能影响技能发展"}, "ai101.cognitiveOffloading.implications.title": {"message": "对学习的影响"}, "ai101.cognitiveOffloading.implications.item1": {"message": "减少持续分析参与机会的风险"}, "ai101.cognitiveOffloading.implications.item2": {"message": "对执行功能发展的潜在影响"}, "ai101.cognitiveOffloading.implications.item3": {"message": "关于智力自主性和适应性的问题"}, "ai101.cognitiveOffloading.implications.item4": {"message": "需要平衡的人-AI认知伙伴关系"}, "ai101.responsibleFrameworks.title": {"message": "负责任AI整合框架"}, "ai101.responsibleFrameworks.subtitle": {"message": "保持高阶思维的系统性方法"}, "ai101.responsibleFrameworks.unesco.title": {"message": "UNESCO AI能力框架"}, "ai101.responsibleFrameworks.unesco.item1": {"message": "以人为中心的思维发展"}, "ai101.responsibleFrameworks.unesco.item2": {"message": "AI伦理理解"}, "ai101.responsibleFrameworks.unesco.item3": {"message": "AI技术和应用掌握"}, "ai101.responsibleFrameworks.unesco.item4": {"message": "AI系统设计参与"}, "ai101.responsibleFrameworks.schoolai.title": {"message": "SchoolAI 4C框架"}, "ai101.responsibleFrameworks.schoolai.item1": {"message": "理解AI能力和局限性"}, "ai101.responsibleFrameworks.schoolai.item2": {"message": "将AI用作学习伙伴"}, "ai101.responsibleFrameworks.schoolai.item3": {"message": "批判性评估AI输出"}, "ai101.responsibleFrameworks.schoolai.item4": {"message": "利用AI进行创意目的"}, "ai101.responsibleFrameworks.extraheric.title": {"message": "FunBlocks AI框架"}, "ai101.responsibleFrameworks.extraheric.content": {"message": "与替代人类认知的传统人-AI交互设计不同，FunBlocks AI通过提出问题或提供替代观点而非直接答案来促进认知参与。这种方法确保学生在受益于AI能力的同时保持积极的分析思维参与。"}, "ai101.responsibleFrameworks.samr.title": {"message": "AI整合的SAMR模型"}, "ai101.responsibleFrameworks.samr.item1": {"message": "AI替代传统工具"}, "ai101.responsibleFrameworks.samr.item2": {"message": "AI增强现有流程"}, "ai101.responsibleFrameworks.samr.item3": {"message": "AI实现重大任务重新设计"}, "ai101.responsibleFrameworks.samr.item4": {"message": "AI创造以前不可能的学习体验"}, "ai101.collaboration.title": {"message": "人-AI协作模型"}, "ai101.collaboration.subtitle": {"message": "互补认知优势实现最佳结果"}, "ai101.collaboration.complementary.title": {"message": "互补优势"}, "ai101.collaboration.human": {"message": "人类优势"}, "ai101.collaboration.ai": {"message": "AI优势"}, "ai101.collaboration.human.item1": {"message": "新颖和创意想法"}, "ai101.collaboration.ai.item1": {"message": "实用、可实施的解决方案"}, "ai101.collaboration.human.item2": {"message": "上下文理解"}, "ai101.collaboration.ai.item2": {"message": "大规模数据处理"}, "ai101.collaboration.human.item3": {"message": "伦理推理"}, "ai101.collaboration.ai.item3": {"message": "模式识别"}, "ai101.collaboration.human.item4": {"message": "情商"}, "ai101.collaboration.ai.item4": {"message": "一致性表现"}, "ai101.collaboration.creative.title": {"message": "AI作为创意伙伴"}, "ai101.collaboration.creative.item1": {"message": "生成多样化的想法和观点"}, "ai101.collaboration.creative.item2": {"message": "挑战假设和偏见"}, "ai101.collaboration.creative.item3": {"message": "提供替代观点"}, "ai101.collaboration.creative.item4": {"message": "支持构思和头脑风暴"}, "ai101.collaboration.metacognitive.title": {"message": "元认知脚手架"}, "ai101.collaboration.metacognitive.item1": {"message": "学习模式分析"}, "ai101.collaboration.metacognitive.item2": {"message": "认知过程洞察"}, "ai101.collaboration.metacognitive.item3": {"message": "反思支持和指导"}, "ai101.collaboration.metacognitive.item4": {"message": "策略推荐"}, "ai101.collaboration.division.title": {"message": "认知劳动的最优分工"}, "ai101.collaboration.division.content": {"message": "AI处理：常规处理、数据分析、初始内容生成\n              \n人类专注：批判性评估、创意综合、伦理推理、最终决策\n\n结果：通过互补协作增强认知结果"}, "ai101.educationalPractice.title": {"message": "对教育实践的影响"}, "ai101.educationalPractice.subtitle": {"message": "重新概念化教学法、评估和学术诚信"}, "ai101.educationalPractice.assignment.title": {"message": "作业设计"}, "ai101.educationalPractice.assignment.item1": {"message": "清晰的学习目标沟通"}, "ai101.educationalPractice.assignment.item2": {"message": "指定适当的AI使用指南"}, "ai101.educationalPractice.assignment.item3": {"message": "要求对AI输出进行批判性分析"}, "ai101.educationalPractice.assignment.item4": {"message": "展示原创思维整合"}, "ai101.educationalPractice.integrity.title": {"message": "学术诚信"}, "ai101.educationalPractice.integrity.item1": {"message": "要求AI使用透明度"}, "ai101.educationalPractice.integrity.item2": {"message": "强调原创思维的重要性"}, "ai101.educationalPractice.integrity.item3": {"message": "学生对所有提交内容负责"}, "ai101.educationalPractice.integrity.item4": {"message": "区分适当使用的细致政策"}, "ai101.educationalPractice.tilt.title": {"message": "AI整合作业的TILT框架"}, "ai101.educationalPractice.tilt.component": {"message": "组件"}, "ai101.educationalPractice.tilt.description": {"message": "描述"}, "ai101.educationalPractice.tilt.transparency": {"message": "清晰沟通学习目标和AI使用期望"}, "ai101.educationalPractice.tilt.instruction": {"message": "关于适当AI应用和局限性的明确指导"}, "ai101.educationalPractice.tilt.learning": {"message": "专注于学习成果和技能发展"}, "ai101.educationalPractice.tilt.testing": {"message": "重视批判性思维和原创性的评估标准"}, "ai101.educationalPractice.curriculum.title": {"message": "AI素养课程开发"}, "ai101.educationalPractice.curriculum.item1": {"message": "跨学科整合AI能力，而非作为独立学科"}, "ai101.educationalPractice.curriculum.item2": {"message": "促进连接STEM和社会研究的跨学科学习"}, "ai101.educationalPractice.curriculum.item3": {"message": "培养对AI能力、局限性和伦理的理解"}, "ai101.educationalPractice.curriculum.item4": {"message": "强调批判性评估和负责任的AI使用"}, "ai101.slide18.title": {"message": "AI沟通的技巧"}, "ai101.slide18.subtitle": {"message": "让AI成为你的得力助手"}, "ai101.slide19.title": {"message": "生成式时代的学习与创造"}, "ai101.slide19.subtitle": {"message": "从稀缺到丰盈"}, "ai101.slide19.paradigm.title": {"message": "范式转变"}, "ai101.slide19.paradigm.traditional": {"message": "传统时代"}, "ai101.slide19.paradigm.generative": {"message": "生成式时代"}, "ai101.slide19.paradigm.traditional.item1": {"message": "信息稀缺"}, "ai101.slide19.paradigm.generative.item1": {"message": "信息丰盈"}, "ai101.slide19.paradigm.traditional.item2": {"message": "内容创作困难"}, "ai101.slide19.paradigm.generative.item2": {"message": "内容创作便捷"}, "ai101.slide19.paradigm.traditional.item3": {"message": "注重记忆"}, "ai101.slide19.paradigm.generative.item3": {"message": "注重批判性评估"}, "ai101.slide19.paradigm.traditional.item4": {"message": "个人学习"}, "ai101.slide19.paradigm.generative.item4": {"message": "AI辅助学习"}, "ai101.slide19.paradigm.traditional.item5": {"message": "线性课程"}, "ai101.slide19.paradigm.generative.item5": {"message": "个性化路径"}, "ai101.slide19.priorities.title": {"message": "新的学习重点"}, "ai101.slide19.priorities.item1": {"message": "培养信息素养和来源评估能力"}, "ai101.slide19.priorities.item2": {"message": "学习与AI工具有效协作"}, "ai101.slide19.priorities.item3": {"message": "注重创造力、批判性思维和问题解决"}, "ai101.slide19.priorities.item4": {"message": "培养情商和人际连接"}, "ai101.slide20.title": {"message": "终身学习的新范式"}, "ai101.slide20.subtitle": {"message": "从阶段性教育到持续成长"}, "ai101.slide21.title": {"message": "培养AI素养"}, "ai101.slide21.subtitle": {"message": "技术应该放大人类潜能，而非取代人类"}, "ai101.slide22.title": {"message": "教育相关AI产品"}, "ai101.slide22.subtitle": {"message": "让AI成为教育助手"}, "ai101.slide23.title": {"message": "FunBlocks AI"}, "ai101.slide23.subtitle": {"message": "与AI共同探索、思考与创造"}, "ai101.slide24.title": {"message": "为什么要借助AI帮助创新，提升思维能力？"}, "ai101.slide24.subtitle": {"message": "突破人类认知局限"}, "ai101.slide25.title": {"message": "突破线性思维的局限"}, "ai101.slide25.subtitle": {"message": "从对话线到无限画布"}, "ai101.slide26.title": {"message": "用AI提升思考能力"}, "ai101.slide26.subtitle": {"message": "让AI辅助思考，但不代替思考"}, "ai101.slide27.title": {"message": "总结与展望"}, "ai101.slide27.subtitle": {"message": "拥抱AI时代的工作和终身学习变革"}, "ai101.slide28.title": {"message": "AI已来，拥抱并塑造未来!"}, "ai101.slide28.subtitle": {"message": "Questions & Discussion"}, "ai101.content.aiTraining.pretraining": {"message": "预训练：从海量文本数据中学习语言模式"}, "ai101.content.aiTraining.supervised": {"message": "监督微调：学习遵循指令"}, "ai101.content.aiTraining.reinforcement": {"message": "强化学习：与人类偏好对齐"}, "ai101.content.neuralNetwork.biological": {"message": "生物神经元传递电信号"}, "ai101.content.neuralNetwork.artificial": {"message": "人工神经元处理数值"}, "ai101.content.neuralNetwork.layers": {"message": "多层结构实现复杂模式识别"}, "ai101.content.math.probability": {"message": "一切皆概率 - 没有绝对确定性"}, "ai101.content.math.statistics": {"message": "通过统计方法从数据中学习模式"}, "ai101.content.math.optimization": {"message": "持续优化寻找最佳解决方案"}, "ai101.content.concepts.parameters": {"message": "参数：神经网络中存储的\"知识\""}, "ai101.content.concepts.tokens": {"message": "Token：文本处理的基本单位"}, "ai101.content.concepts.attention": {"message": "注意力：专注于相关信息的机制"}, "ai101.content.intelligence.compression": {"message": "智能可能是高效压缩信息的能力"}, "ai101.content.intelligence.patterns": {"message": "找到解释复杂现象的最简规律"}, "ai101.content.scaling.bigger": {"message": "更多参数 → 更好性能"}, "ai101.content.scaling.data": {"message": "更多数据 → 更多知识"}, "ai101.content.scaling.compute": {"message": "更多计算 → 更好训练"}, "ai101.content.understanding.statistical": {"message": "AI擅长统计模式匹配"}, "ai101.content.understanding.semantic": {"message": "但它真的理解含义吗？"}, "ai101.content.agi.current": {"message": "当前AI：狭窄的专门能力"}, "ai101.content.agi.future": {"message": "AGI目标：跨所有领域的通用智能"}, "ai101.content.threats.job": {"message": "某些行业的工作岗位替代"}, "ai101.content.threats.misinformation": {"message": "生成虚假信息的潜在风险"}, "ai101.content.threats.bias": {"message": "放大现有偏见"}, "ai101.content.survival.adapt": {"message": "拥抱变化和持续学习"}, "ai101.content.survival.collaborate": {"message": "学会与AI作为伙伴协作"}, "ai101.content.survival.human": {"message": "专注于独特的人类能力"}, "ai101.slide18.clearPrompting.title": {"message": "懒人提示法"}, "ai101.slide18.clearPrompting.content": {"message": "目标清晰、指令简洁，同时提供丰富的上下文信息"}, "ai101.slide18.iterativeRefinement.title": {"message": "迭代优化"}, "ai101.slide18.iterativeRefinement.content": {"message": "通过反馈和优化持续改进结果"}, "ai101.slide18.rolePlaying.title": {"message": "角色扮演"}, "ai101.slide18.rolePlaying.content": {"message": "引导AI采用特定角色或视角"}, "ai101.slide18.patterns.title": {"message": "懒人提示法，避免微管理"}, "ai101.slide18.patterns.content": {"message": "过度微管理可能会限制AI的潜力。过于具体的指令反映了人的认知偏见，来自个人经验和技能，这可能会限制AI的创新能力和专业水平。实际上，AI在许多方面可能已经超出了人类的能力和预期"}, "ai101.slide24.cognitiveAugmentation.title": {"message": "认知增强"}, "ai101.slide24.cognitiveAugmentation.content": {"message": "通过AI辅助增强人类思维能力"}, "ai101.slide24.patternRecognition.title": {"message": "模式识别"}, "ai101.slide24.patternRecognition.content": {"message": "识别多个视角下的复杂模式和关系"}, "ai101.slide24.speedScale.title": {"message": "速度和规模"}, "ai101.slide24.speedScale.content": {"message": "以前所未有的速度处理和分析信息"}, "ai101.slide24.limitations.title": {"message": "理解局限性"}, "ai101.slide24.limitations.item1": {"message": "工作记忆限制"}, "ai101.slide24.limitations.item2": {"message": "确认偏差和认知偏差"}, "ai101.slide24.limitations.item3": {"message": "复杂信息处理速度限制"}, "ai101.slide24.limitations.item4": {"message": "难以从多个视角看到模式"}, "ai101.slide24.limitations.item5": {"message": "有限的知识和视角"}, "ai101.slide24.partner.title": {"message": "AI作为思维伙伴"}, "ai101.slide24.partner.content": {"message": "结合人类批判性思维和AI处理能力"}, "ai101.slide25.comparison.title": {"message": "线性对话 vs 多视角探索"}, "ai101.slide25.comparison.linear": {"message": "线性对话"}, "ai101.slide25.comparison.network": {"message": "多视角探索"}, "ai101.slide25.comparison.linear.item1": {"message": "单向对话"}, "ai101.slide25.comparison.network.item1": {"message": "多向探索"}, "ai101.slide25.comparison.linear.item2": {"message": "单一视角"}, "ai101.slide25.comparison.network.item2": {"message": "多重视角"}, "ai101.slide25.comparison.linear.item3": {"message": "眼界越来越狭窄"}, "ai101.slide25.comparison.network.item3": {"message": "眼界越来越广阔"}, "ai101.slide25.comparison.linear.item4": {"message": "适用于快速获得答案"}, "ai101.slide25.comparison.network.item4": {"message": "适用于深入思考和探索"}, "ai101.slide25.comparison.linear.item5": {"message": "专注于结果"}, "ai101.slide25.comparison.network.item5": {"message": "专注于过程"}, "ai101.slide25.benefits.title": {"message": "多视角思维优势"}, "ai101.slide25.benefits.item1": {"message": "通过多个视角和连接增强创造力"}, "ai101.slide25.benefits.item2": {"message": "通过多个视角获得更好的问题解决能力"}, "ai101.slide25.benefits.item3": {"message": "通过批判性思维和可视化改进学习和记忆"}, "ai101.slide25.benefits.item4": {"message": "通过多个视角获得更全面的理解"}, "ai101.slide25.benefits.item5": {"message": "支持复杂问题分解，分而治之"}, "ai101.slide26.partner.title": {"message": "AI作为伙伴"}, "ai101.slide26.partner.content": {"message": "与AI协作扩展思维能力"}, "ai101.slide26.analysis.title": {"message": "增强分析"}, "ai101.slide26.analysis.content": {"message": "处理和分析复杂信息模式"}, "ai101.slide26.creative.title": {"message": "创意催化剂"}, "ai101.slide26.creative.content": {"message": "生成和探索新想法和可能性"}, "ai101.slide26.strategies.title": {"message": "思维增强策略"}, "ai101.slide26.strategies.item1": {"message": "使用AI进行初步想法生成"}, "ai101.slide26.strategies.item2": {"message": "应用人类判断进行优化"}, "ai101.slide26.strategies.item3": {"message": "结合多个视角"}, "ai101.slide26.strategies.item4": {"message": "持续迭代和改进"}, "ai101.slide26.strategies.item5": {"message": "保持批判性思维"}, "ai101.slide26.process.content": {"message": "结合人类批判性思维和AI处理能力以增强决策"}, "ai101.slide27.transformation.title": {"message": "教育转型"}, "ai101.slide27.transformation.content": {"message": "适应新的学习范式和机遇"}, "ai101.slide27.partnership.title": {"message": "人机协作"}, "ai101.slide27.partnership.content": {"message": "利用人类和人工智能的优势"}, "ai101.slide27.adaptation.title": {"message": "持续适应"}, "ai101.slide27.adaptation.content": {"message": "紧跟AI能力和应用的发展"}, "ai101.slide27.takeaways.title": {"message": "关键要点"}, "ai101.slide27.takeaways.item1": {"message": "AI是增强人类能力的工具"}, "ai101.slide27.takeaways.item2": {"message": "专注于发展独特的人类技能"}, "ai101.slide27.takeaways.item3": {"message": "拥抱持续学习和适应"}, "ai101.slide27.takeaways.item4": {"message": "保持伦理意识和责任感"}, "ai101.slide27.takeaways.item5": {"message": "建立有效的人机协作"}, "ai101.slide27.takeaways.item6": {"message": "用AI塑造教育的未来"}, "ai101.slide28.connect.title": {"message": "联系FunBlocks AI"}, "ai101.slide28.connect.website.title": {"message": "网站"}, "ai101.slide28.connect.website.content": {"message": "访问我们的网站获取更多资源和更新"}, "ai101.slide28.connect.contact.title": {"message": "联系方式"}, "ai101.slide28.connect.contact.content": {"message": "联系我们进行合作和支持"}, "ai101.slide20.continuous.title": {"message": "持续学习"}, "ai101.slide20.continuous.content": {"message": "将学习作为贯穿一生的持续过程"}, "ai101.slide20.justInTime.title": {"message": "即时学习"}, "ai101.slide20.justInTime.content": {"message": "在需要时和需要的地方学习"}, "ai101.slide20.collaborative.title": {"message": "协作学习"}, "ai101.slide20.collaborative.content": {"message": "通过人机协作进行学习"}, "ai101.slide20.infrastructure.title": {"message": "构建学习基础设施"}, "ai101.slide20.infrastructure.content": {"message": "提升学习力，发展元学习能力"}, "ai101.slide21.understanding.title": {"message": "理解AI"}, "ai101.slide21.understanding.content": {"message": "掌握AI的能力、局限性和应用"}, "ai101.slide21.ethics.title": {"message": "伦理意识"}, "ai101.slide21.ethics.content": {"message": "理解伦理影响和责任"}, "ai101.slide21.skills.title": {"message": "实践技能"}, "ai101.slide21.skills.content": {"message": "发展AI交互的实际能力"}, "ai101.slide21.curriculum.title": {"message": "AI素养课程"}, "ai101.slide21.curriculum.item1": {"message": "AI基础知识和概念"}, "ai101.slide21.curriculum.item2": {"message": "伦理考虑和影响"}, "ai101.slide21.curriculum.item3": {"message": "实用AI工具和应用"}, "ai101.slide21.curriculum.item4": {"message": "批判性思维和评估"}, "ai101.slide21.curriculum.item5": {"message": "未来趋势和发展"}, "ai101.slide22.conversational.title": {"message": "对话式AI"}, "ai101.slide22.conversational.content": {"message": "交互式学习和讨论助手"}, "ai101.slide22.writing.title": {"message": "写作助手"}, "ai101.slide22.writing.content": {"message": "内容创作和编辑工具"}, "ai101.slide22.creative.title": {"message": "创意工具"}, "ai101.slide22.creative.content": {"message": "AI驱动的设计和多媒体创作"}, "ai101.slide22.data.title": {"message": "研究工具"}, "ai101.slide22.data.content": {"message": "信息检索、总结、分析工具"}, "ai101.slide22.learning.title": {"message": "学习平台"}, "ai101.slide22.learning.content": {"message": "AI增强的教育平台"}, "ai101.slide22.thinking.title": {"message": "思维工具"}, "ai101.slide22.thinking.content": {"message": "增强认知过程的工具"}, "ai101.slide23.mindMapping.title": {"message": "分析和批判性思维"}, "ai101.slide23.mindMapping.content": {"message": "细化问题、分析谬误、发展批判性思维"}, "ai101.slide23.brainstorming.title": {"message": "创新思维"}, "ai101.slide23.brainstorming.content": {"message": "与AI一起生成和发展创意想法"}, "ai101.slide23.exploration.title": {"message": "无限探索"}, "ai101.slide23.exploration.content": {"message": "使用AI在无限画布上从多个视角探索无限可能"}, "ai101.slide23.structuredThinking.title": {"message": "AI增强的思维"}, "ai101.slide23.structuredThinking.content": {"message": "AI生成思维导图和头脑风暴"}, "ai101.slide23.mentalModels.title": {"message": "AI驱动的经典思维模型应用"}, "ai101.slide23.mentalModels.content": {"message": "使用AI辅助应用经典心理模型解决复杂问题"}, "ai101.slide23.creativeWorkflows.title": {"message": "创意工作流"}, "ai101.slide23.creativeWorkflows.content": {"message": "从概念到演示的集成AI工具"}, "ai101.slide23.features.title": {"message": "特色功能"}, "ai101.slide23.features.item1": {"message": "AI 思维导图和头脑风暴"}, "ai101.slide23.features.item2": {"message": "AI 协作思考空间"}, "ai101.slide23.features.item3": {"message": "与主流 AI 模型集成"}, "ai101.slide23.features.item4": {"message": "AI 驱动的批判性分析"}, "ai101.slide23.features.item5": {"message": "AI 提出问题或提供不同视角"}, "ai101.genai.coreRelatedTech.title": {"message": "核心相关技术"}, "ai101.genai.coreRelatedTech.item1": {"message": "深度神经网络"}, "ai101.genai.coreRelatedTech.item2": {"message": "注意力机制"}, "ai101.genai.coreRelatedTech.item3": {"message": "预训练与微调范式"}, "ai101.genai.coreRelatedTech.item4": {"message": "强化学习对齐"}, "ai101.coreLearning.supervisedLearning.title": {"message": "监督学习"}, "ai101.coreLearning.supervisedLearning.content": {"message": "从标注数据中学习输入输出映射"}, "ai101.coreLearning.supervisedLearning.examples": {"message": "例如：图像分类、情感分析"}, "ai101.coreLearning.reinforcementLearning.title": {"message": "强化学习"}, "ai101.coreLearning.reinforcementLearning.content": {"message": "通过试错和奖励信号优化策略"}, "ai101.coreLearning.reinforcementLearning.examples": {"message": "例如：游戏AI、机器人控制"}, "ai101.coreLearning.deepLearning.title": {"message": "深度学习"}, "ai101.coreLearning.deepLearning.content": {"message": "多层神经网络自动提取特征"}, "ai101.coreLearning.deepLearning.examples": {"message": "端到端学习复杂模式"}, "ai101.coreLearning.powerCombining.title": {"message": "三者结合的力量"}, "ai101.coreLearning.powerCombining.content": {"message": "监督学习奠定基础 → 深度学习提取特征 → 强化学习优化行为"}, "ai101.aiTraining.stage1.title": {"message": "阶段1：预训练"}, "ai101.aiTraining.stage2.title": {"message": "阶段2：监督微调"}, "ai101.aiTraining.stage3.title": {"message": "阶段3：强化学习"}, "ai101.aiTraining.process": {"message": "原始文本 → 语言模型 → 指令跟随者 → 人类对齐AI"}, "ai101.neuralNetwork.biologicalNeurons.title": {"message": "生物神经元"}, "ai101.neuralNetwork.artificialNeurons.title": {"message": "人工神经元"}, "ai101.neuralNetwork.deepNetworks.title": {"message": "深度网络"}, "ai101.neuralNetwork.architecture.title": {"message": "网络架构"}, "ai101.neuralNetwork.architecture.content": {"message": "输入层 → 隐藏层 → 输出层\n    ↓        ↓        ↓\n  原始数据 → 特征提取 → 预测结果"}, "ai101.math.probabilityTheory.title": {"message": "概率论"}, "ai101.math.statistics.title": {"message": "统计学"}, "ai101.math.optimization.title": {"message": "优化理论"}, "ai101.math.coreConcepts.title": {"message": "核心数学概念"}, "ai101.math.coreConcepts.item1": {"message": "线性代数：向量空间和变换"}, "ai101.math.coreConcepts.item2": {"message": "微积分：梯度下降和反向传播"}, "ai101.math.coreConcepts.item3": {"message": "信息论：熵和压缩"}, "ai101.math.coreConcepts.item4": {"message": "图论：网络结构和关系"}, "ai101.keyConcepts.parameters.title": {"message": "参数"}, "ai101.keyConcepts.tokens.title": {"message": "Token"}, "ai101.keyConcepts.attention.title": {"message": "注意力"}, "ai101.keyConcepts.scaleComparison.title": {"message": "规模对比"}, "ai101.keyConcepts.table.model": {"message": "模型"}, "ai101.keyConcepts.table.parameters": {"message": "参数"}, "ai101.keyConcepts.table.trainingData": {"message": "训练数据"}, "ai101.keyConcepts.table.humanBrain": {"message": "人类大脑"}, "ai101.keyConcepts.table.lifetimeExperience": {"message": "终身经验"}, "ai101.intelligence.informationCompression.title": {"message": "信息压缩"}, "ai101.intelligence.patternRecognition.title": {"message": "模式识别"}, "ai101.intelligence.asCompression.title": {"message": "智能即压缩"}, "ai101.intelligence.asCompression.content": {"message": "原始数据（TB级） → 压缩知识（GB级） → 预测\n\n例如：从数十亿单词中学习语言\n→ 压缩为语法规则和模式\n→ 生成连贯的新句子"}, "ai101.scaling.moreParameters.title": {"message": "更多参数"}, "ai101.scaling.moreData.title": {"message": "更多数据"}, "ai101.scaling.moreCompute.title": {"message": "更多计算"}, "ai101.scaling.trends.title": {"message": "规模化趋势"}, "ai101.scaling.trends.item1": {"message": "性能随规模可预测地提升"}, "ai101.scaling.trends.item2": {"message": "在特定阈值出现涌现能力"}, "ai101.scaling.trends.item3": {"message": "但规模化有物理和经济限制"}, "ai101.scaling.trends.item4": {"message": "效率改进变得至关重要"}, "ai101.understanding.statisticalMastery.title": {"message": "统计掌握"}, "ai101.understanding.semanticUnderstanding.title": {"message": "语义理解？"}, "ai101.understanding.chineseRoom.title": {"message": "中文房间论证"}, "ai101.understanding.chineseRoom.content": {"message": "房间里的人按规则回应中文字符\n↓\n看起来理解中文，但实际上不懂\n↓\n类似地，AI可能模拟理解而非真正理解"}, "ai101.understanding.currentEvidence.title": {"message": "当前证据"}, "ai101.understanding.currentEvidence.item1": {"message": "AI展现出卓越的语言能力"}, "ai101.understanding.currentEvidence.item2": {"message": "能够推理抽象概念"}, "ai101.understanding.currentEvidence.item3": {"message": "但缺乏真实世界的基础经验"}, "ai101.understanding.currentEvidence.item4": {"message": "理解vs复杂模式匹配仍有争议"}, "ai101.agi.currentState.title": {"message": "当前状态"}, "ai101.agi.agiVision.title": {"message": "AGI愿景"}, "ai101.agi.vsHuman.title": {"message": "AI vs 人类能力"}, "ai101.agi.table.domain": {"message": "领域"}, "ai101.agi.table.aiStatus": {"message": "AI状态"}, "ai101.agi.table.humanLevel": {"message": "人类水平"}, "ai101.agi.table.surpassed": {"message": "已超越"}, "ai101.agi.table.matched": {"message": "已匹配"}, "ai101.agi.table.nearHuman": {"message": "接近人类"}, "ai101.agi.table.belowHuman": {"message": "低于人类"}, "ai101.agi.table.debated": {"message": "有争议"}, "ai101.threats.jobDisplacement.title": {"message": "工作替代"}, "ai101.threats.misinformation.title": {"message": "虚假信息"}, "ai101.threats.biasAmplification.title": {"message": "偏见放大"}, "ai101.threats.mitigation.title": {"message": "缓解策略"}, "ai101.threats.mitigation.item1": {"message": "制定AI治理和监管框架"}, "ai101.threats.mitigation.item2": {"message": "投资教育和技能再培训项目"}, "ai101.threats.mitigation.item3": {"message": "推广负责任的AI开发实践"}, "ai101.threats.mitigation.item4": {"message": "促进人机协作而非替代"}, "ai101.survival.continuousLearning.title": {"message": "持续学习"}, "ai101.survival.aiCollaboration.title": {"message": "AI协作"}, "ai101.survival.humanUniqueness.title": {"message": "人类独特性"}, "ai101.survival.strategicApproach.title": {"message": "战略方法"}, "ai101.survival.strategicApproach.content": {"message": "短期：学习AI工具和工作流程\n中期：发展人机协作技能\n长期：专注于创造力、同理心和复杂推理"}, "ai101.agi.table.chessGo": {"message": "国际象棋/围棋"}, "ai101.agi.table.imageRecognition": {"message": "图像识别"}, "ai101.agi.table.languageTasks": {"message": "语言任务"}, "ai101.agi.table.generalReasoning": {"message": "通用推理"}, "ai101.agi.table.creativity": {"message": "创造力"}, "ai101.agi.table.superhuman": {"message": "✅ 超人类"}, "ai101.agi.table.humanLevelStatus": {"message": "✅ 人类水平"}, "ai101.agi.table.approaching": {"message": "🔄 接近中"}, "ai101.agi.table.uncertain": {"message": "❓ 不确定"}, "ai101.agi.table.emerging": {"message": "🎨 新兴中"}, "ai101.timeline.1950": {"message": "图灵提出了图灵测试"}, "ai101.timeline.1956": {"message": "达特茅斯会议，AI概念诞生"}, "ai101.timeline.1980s": {"message": "专家系统的兴起和AI寒冬"}, "ai101.timeline.2000s": {"message": "机器学习复兴"}, "ai101.timeline.2012": {"message": "深度学习突破（AlexNet）"}, "ai101.timeline.2017": {"message": "Transformer架构出现"}, "ai101.timeline.2022": {"message": "ChatGPT引发生成式AI革命"}, "ai101.whyNow.computing.title": {"message": "计算能力突破"}, "ai101.whyNow.computing.item1": {"message": "GPU并行计算革命"}, "ai101.whyNow.computing.item2": {"message": "云计算降低门槛"}, "ai101.whyNow.computing.item3": {"message": "专用AI芯片的出现"}, "ai101.whyNow.data.title": {"message": "数据爆炸"}, "ai101.whyNow.data.item1": {"message": "互联网内容呈现爆炸式增长"}, "ai101.whyNow.data.item2": {"message": "数字化进程加速"}, "ai101.whyNow.data.item3": {"message": "成熟的数据标注技术"}, "ai101.whyNow.technical.title": {"message": "技术架构"}, "ai101.whyNow.technical.item1": {"message": "Transformer革命性突破"}, "ai101.whyNow.technical.item2": {"message": "注意力机制创新"}, "ai101.whyNow.technical.item3": {"message": "端到端学习范式"}, "ai101.concept.hierarchy": {"message": "AGI（人工通用智能）\n    ↑ 目标方向\nGenAI（生成式AI）\n    ↑ 当前阶段\nLLM（大型语言模型）\n    ↑ 核心技术"}, "ai101.concept.llm.title": {"message": "LLM"}, "ai101.concept.llm.content": {"message": "基于Transformer架构的大规模语言模型"}, "ai101.concept.genai.title": {"message": "GenAI"}, "ai101.concept.genai.content": {"message": "能够生成文本、图像、音频和其他内容的AI系统"}, "ai101.concept.agi.title": {"message": "AGI"}, "ai101.concept.agi.content": {"message": "在所有认知任务中与人类匹敌或超越的智能"}, "ai101.genai.differences.traditional": {"message": "传统AI"}, "ai101.genai.differences.generative": {"message": "生成式AI"}, "ai101.genai.differences.traditional.item1": {"message": "识别和分类"}, "ai101.genai.differences.generative.item1": {"message": "内容创作"}, "ai101.genai.differences.traditional.item2": {"message": "规则驱动"}, "ai101.genai.differences.generative.item2": {"message": "数据驱动"}, "ai101.genai.differences.traditional.item3": {"message": "专用系统"}, "ai101.genai.differences.generative.item3": {"message": "通用能力"}, "ai101.genai.differences.traditional.item4": {"message": "确定性输出"}, "ai101.genai.differences.generative.item4": {"message": "概率生成"}, "ai101.generativeEra.title": {"message": "生成式AI时代的学习"}, "ai101.generativeEra.subtitle": {"message": "用AI改变教育"}, "ai101.generativeEra.personalized.title": {"message": "个性化学习"}, "ai101.generativeEra.personalized.content": {"message": "AI适应个体学习风格和速度"}, "ai101.generativeEra.interactive.title": {"message": "互动内容"}, "ai101.generativeEra.interactive.content": {"message": "动态和引人入胜的学习材料"}, "ai101.generativeEra.assessment.title": {"message": "智能评估"}, "ai101.generativeEra.assessment.content": {"message": "实时反馈和进度跟踪"}, "ai101.generativeEra.collaboration.title": {"message": "增强协作"}, "ai101.generativeEra.collaboration.content": {"message": "AI促进的小组学习和讨论"}, "ai101.gpt.title": {"message": "GPT (Generative Pre-trained Transformers)"}, "ai101.gpt.subtitle": {"message": "理解现代AI的核心技术"}, "ai101.gpt.definition.title": {"message": "核心定义"}, "ai101.gpt.definition.item1": {"message": "生成式预训练变换器：基于Transformer架构的大型语言模型"}, "ai101.gpt.definition.item2": {"message": "自回归语言模型：通过预测下一个词来生成文本"}, "ai101.gpt.definition.item3": {"message": "无监督预训练 + 有监督微调：两阶段训练范式的典型代表"}, "ai101.gpt.features.title": {"message": "关键特征"}, "ai101.gpt.features.item1": {"message": "单向注意力机制：只能看到前面的文本，适合文本生成任务"}, "ai101.gpt.features.item2": {"message": "大规模参数：从GPT-1的1.17亿到GPT-4的数千亿参数"}, "ai101.gpt.features.item3": {"message": "强大的零样本和少样本学习能力"}, "ai101.gptArchitecture.title": {"message": "GPT核心技术架构"}, "ai101.gptArchitecture.subtitle": {"message": "深入理解GPT的技术基础"}, "ai101.gptArchitecture.transformer.title": {"message": "Transformer Decoder"}, "ai101.gptArchitecture.transformer.item1": {"message": "多头自注意力：捕捉长距离依赖关系"}, "ai101.gptArchitecture.transformer.item2": {"message": "位置编码：理解文本序列中的位置信息"}, "ai101.gptArchitecture.transformer.item3": {"message": "残差连接 + 层归一化：稳定训练过程"}, "ai101.gptArchitecture.pretraining.title": {"message": "预训练策略"}, "ai101.gptArchitecture.pretraining.item1": {"message": "下一词预测：在大规模文本语料上学习语言规律"}, "ai101.gptArchitecture.pretraining.item2": {"message": "因果掩码：确保只能访问前面的词汇"}, "ai101.gptArchitecture.pretraining.item3": {"message": "大规模数据：互联网文本、书籍、新闻等多样化数据源"}, "ai101.gptArchitecture.alignment.title": {"message": "微调与对齐"}, "ai101.gptArchitecture.alignment.item1": {"message": "指令微调：提升模型遵循指令的能力"}, "ai101.gptArchitecture.alignment.item2": {"message": "基于人类反馈的强化学习（RLHF）：使模型输出更符合人类偏好"}, "ai101.gptArchitecture.alignment.item3": {"message": "安全过滤：减少有害内容的生成"}, "ai101.gptCapabilities.title": {"message": "GPT核心能力与应用"}, "ai101.gptCapabilities.subtitle": {"message": "从文本生成到智能推理"}, "ai101.gptCapabilities.textGeneration.title": {"message": "文本生成能力"}, "ai101.gptCapabilities.textGeneration.item1": {"message": "创意写作：故事、诗歌、剧本创作"}, "ai101.gptCapabilities.textGeneration.item2": {"message": "技术文档：API文档、用户手册、技术报告"}, "ai101.gptCapabilities.textGeneration.item3": {"message": "营销内容：广告文案、产品描述、社交媒体内容"}, "ai101.gptCapabilities.understanding.title": {"message": "理解与推理"}, "ai101.gptCapabilities.understanding.item1": {"message": "阅读理解：回答基于文本的复杂问题"}, "ai101.gptCapabilities.understanding.item2": {"message": "逻辑推理：解决数学问题和逻辑谜题"}, "ai101.gptCapabilities.understanding.item3": {"message": "知识问答：跨领域的百科知识查询"}, "ai101.gptCapabilities.coding.title": {"message": "代码生成"}, "ai101.gptCapabilities.coding.item1": {"message": "程序编写：根据需求生成代码"}, "ai101.gptCapabilities.coding.item2": {"message": "代码解释：理解和注释现有代码"}, "ai101.gptCapabilities.coding.item3": {"message": "调试辅助：发现和修复代码错误"}, "ai101.gptLimitations.title": {"message": "GPT技术优势与局限"}, "ai101.gptLimitations.subtitle": {"message": "理性认识GPT的能力边界"}, "ai101.gptLimitations.advantages.title": {"message": "主要优势"}, "ai101.gptLimitations.advantages.item1": {"message": "泛化能力强：一个模型处理多种任务"}, "ai101.gptLimitations.advantages.item2": {"message": "上下文学习：通过示例快速适应新任务"}, "ai101.gptLimitations.advantages.item3": {"message": "创造性输出：生成新颖且有用的内容"}, "ai101.gptLimitations.limitations.title": {"message": "当前局限"}, "ai101.gptLimitations.limitations.item1": {"message": "幻觉问题：可能生成看似合理但实际错误的信息"}, "ai101.gptLimitations.limitations.item2": {"message": "知识截止：训练数据存在时间限制"}, "ai101.gptLimitations.limitations.item3": {"message": "计算成本高：推理需要大量计算资源"}, "ai101.gptLimitations.limitations.item4": {"message": "可解释性差：难以理解模型的决策过程"}, "ai101.conceptsExplained.title": {"message": "参数、维度、Token概念解析"}, "ai101.conceptsExplained.subtitle": {"message": "理解大语言模型的核心概念"}, "ai101.conceptsExplained.token.title": {"message": "To<PERSON>（词元）"}, "ai101.conceptsExplained.token.definition": {"message": "定义：token是模型处理文本的最小单位，可以是单个字、词，甚至是部分词。由分词器（Tokenizer）将原始文本切分而成。"}, "ai101.conceptsExplained.token.examples": {"message": "例子：\n英文中：\"ChatGPT is great\" 可能被分成：[\"Chat\", \"G\", \"PT\", \" is\", \" great\"]\n中文中：\"大模型很好用\" 可能被分成：[\"大\", \"模型\", \"很\", \"好\", \"用\"]"}, "ai101.conceptsExplained.token.analogy": {"message": "类比：如果你把一句话看作一段积木搭的墙，tokens就是每一块积木。"}, "ai101.conceptsExplained.dimensions.title": {"message": "维（维度 / Dimensions）"}, "ai101.conceptsExplained.dimensions.definition": {"message": "定义：维是向量或矩阵中每个数据点的位置或\"长度\"。常用于描述隐藏层中的向量空间大小。"}, "ai101.conceptsExplained.dimensions.usage": {"message": "常见使用：\n词向量的维度（embedding size）：比如一个单词被映射为一个768维的向量。\n隐藏层维度（hidden size）：表示每层中每个神经元输出的向量长度。"}, "ai101.conceptsExplained.dimensions.analogy": {"message": "类比：如果一个token是一个商品，维度就是它的\"特征标签\"数量，比如颜色、大小、用途等。"}, "ai101.conceptsExplained.parameters.title": {"message": "参数（Parameters）"}, "ai101.conceptsExplained.parameters.definition": {"message": "定义：参数是模型在训练中学到的\"知识\"。它们包括神经网络中的权重（weights）和偏置（biases）。"}, "ai101.conceptsExplained.parameters.scale": {"message": "数量级：GPT-3有1750亿个参数，GPT-4据推测参数更多。"}, "ai101.conceptsExplained.parameters.analogy": {"message": "类比：把模型比作一个大脑，参数就是它大脑中形成的\"记忆连接\"或\"经验\"。"}, "ai101.conceptsExplained.comparison.title": {"message": "总结对比"}, "homepage.head.title": {"message": "一体化AI工作空间 - 从思维导图到幻灯片和文档"}, "homepage.head.description": {"message": "FunBlocks AI是一个集AI白板、思维导图、幻灯片创建和文档工具于一体的工作空间，帮助您通过创新的思维方式和生产力工具，提升创造力和生产力。"}, "homepage.nav.home": {"message": "FunBlocks AI"}, "homepage.nav.aiflow": {"message": "AIFlow"}, "homepage.nav.tools": {"message": "快速开始"}, "homepage.nav.useCases": {"message": "使用场景"}, "homepage.nav.workspace": {"message": "工作空间"}, "homepage.nav.pricing": {"message": "价格"}, "homepage.nav.trial": {"message": "免费试用"}, "homepage.hero.title": {"message": "与AI共同探索、思考与创造"}, "homepage.hero.subtitle": {"message": "一个革新思维并提升生产力的AI驱动平台。利用AI生成想法，通过思维导图探索主题，并将它们转化为可行的策略、引人入胜的演示和富有洞察力的信息图表。"}, "homepage.hero.trial": {"message": "立即免费试用"}, "homepage.hero.learnMore": {"message": "了解更多"}, "homepage.beyond.title": {"message": "超越ChatGPT"}, "homepage.beyond.description": {"message": "文本聊天框并不是与AI互动的唯一方式。以真正增强您的认知过程的格式可视化您的想法和AI的输出。"}, "homepage.beyond.visualize.title": {"message": "复杂思维可视化"}, "homepage.beyond.visualize.description": {"message": "将线性对话转化为多维思维导图，揭示文本无法传达的连接、模式和洞察。"}, "homepage.beyond.accelerate.title": {"message": "提升创造力，助力创新"}, "homepage.beyond.accelerate.description": {"message": "结合AI的广泛知识与视觉思维技术，突破创造性障碍，产生突破性想法。"}, "homepage.beyond.streamline.title": {"message": "一站式工作流变革学习与工作效率"}, "homepage.beyond.streamline.description": {"message": "将复杂信息转化为结构化的视觉输出，可以立即转换为专业交付物。"}, "homepage.features.visualize.title": {"message": "复杂思维可视化"}, "homepage.features.visualize.description": {"message": "传统的AI聊天机器人将思维限制在线性对话中。FunBlocks AIFlow通过无限画布上的多维思维导图扩展您的认知视野。"}, "homepage.features.visualize.point1": {"message": "从多个角度同时探索任何主题，揭示线性思维所遗漏的洞察"}, "homepage.features.visualize.point2": {"message": "在保持完整上下文的同时，无限深入任何子主题，创建全面的知识结构"}, "homepage.features.visualize.point3": {"message": "让AI生成一个完整的探索空间，包含您未曾考虑的相关主题和问题"}, "homepage.features.visualize.point4": {"message": "通过AI优化的问题来精炼您的思维，针对复杂问题的核心"}, "homepage.features.accelerate.title": {"message": "提升创造力，助力创新"}, "homepage.features.accelerate.description": {"message": "超越基于文本的AI互动的创造性限制。FunBlocks AIFlow结合AI洞察与强大的视觉框架，激发真正的创新。"}, "homepage.features.accelerate.point1": {"message": "将AI的广泛知识与视觉头脑风暴相结合，生成人类思维可能无法自然产生的连接"}, "homepage.features.accelerate.point2": {"message": "通过视觉方式分解复杂问题，以识别根本原因和隐藏机会"}, "homepage.features.accelerate.point3": {"message": "在思维导图中直接应用经典思维模型（如SCAMPER、六顶思考帽和第一原则），并获得AI的帮助"}, "homepage.features.accelerate.point4": {"message": "通过在多个方向上视觉扩展思维空间来克服创造性障碍"}, "homepage.features.streamline.title": {"message": "一站式工作流变革学习与工作效率"}, "homepage.features.streamline.description": {"message": "形成你的创造性工作流。FunBlocks AIFlow将复杂的知识工作转变为视觉化、协作的过程，产生即时、可触及的输出。"}, "homepage.features.streamline.point1": {"message": "在AI的帮助下，立即将压倒性的项目分解为可管理的视觉组件"}, "homepage.features.streamline.point2": {"message": "通过直观的视觉界面与AI互动，匹配您大脑自然处理复杂信息的方式"}, "homepage.features.streamline.point3": {"message": "与AI合作，同时探索多条解决路径，更快识别最佳方法"}, "homepage.features.streamline.point4": {"message": "将您的视觉思维直接转化为专业交付物——幻灯片、信息图表、文档——只需点击一下"}, "homepage.tools.title": {"message": "专为多种场景设计的创新AI工具"}, "homepage.tools.description": {"message": "FunBlocks AIFlow是一个全面的创造力和生产力平台，拥有专门为特定认知挑战设计的工具。"}, "homepage.tools.tools_list": {"message": "AI 工具列表"}, "homepage.tools.tool1.title": {"message": "AI思维导图生成器"}, "homepage.tools.tool1.description": {"message": "轻松将文档、书籍和电影转化为思维导图，帮助您快速理清复杂思路。"}, "homepage.tools.tool2.title": {"message": "AI头脑风暴"}, "homepage.tools.tool2.description": {"message": "通过结合AI与经典思维模型，针对特定主题或问题进行头脑风暴，激发创意和创新解决方案。"}, "homepage.tools.tool3.title": {"message": "AI批判性思维"}, "homepage.tools.tool3.description": {"message": "提升您的批判性思维能力，帮助您在提问、分析和反思中获得多角度的洞察。"}, "homepage.tools.tool4.title": {"message": "AI幻灯片生成器"}, "homepage.tools.tool4.description": {"message": "根据任意主题快速生成专业幻灯片和演讲方案，助您轻松准备演示。"}, "homepage.tools.tool5.title": {"message": "AI信息图生成器"}, "homepage.tools.tool5.description": {"message": "根据输入的文本自动生成信息图和知识卡片，帮助您以视觉方式传达信息。"}, "homepage.tools.tool6.title": {"message": "AI艺术洞察"}, "homepage.tools.tool6.description": {"message": "通过拍摄旅游和博物馆的照片，获得更深入的艺术分析和欣赏体验。"}, "homepage.tools.tool7.title": {"message": "AI教育工具"}, "homepage.tools.tool7.description": {"message": "结合Bloom等教育理论，将主题分解为渐进式认知层次，提升学习效率和教学效果。"}, "homepage.tools.tool8.title": {"message": "AI心理洞察"}, "homepage.tools.tool8.description": {"message": "随时提供心理咨询和解梦服务，帮助您更好地理解自己的内心世界。"}, "homepage.tools.tool9.title": {"message": "AI图像生成器"}, "homepage.tools.tool9.description": {"message": "AI一键生成您所需风格的头像和图像，满足您的个性化需求。"}, "homepage.multiModel.title": {"message": "一站式获取所有领先AI模型能力"}, "homepage.multiModel.description": {"message": "通过单一FunBlocks AI订阅即可访问所有主流大语言模型。无需管理多个订阅或在平台间切换。"}, "homepage.multiModel.advantage1.title": {"message": "一个订阅，所有模型"}, "homepage.multiModel.advantage1.description": {"message": "通过单一FunBlocks订阅即可访问所有领先AI模型。无需管理多个账户或支付额外费用。"}, "homepage.multiModel.advantage2.title": {"message": "显著节省成本"}, "homepage.multiModel.advantage2.description": {"message": "相比单独订阅每个AI平台，可节省高达70%的成本。一个订阅即可解锁所有高级模型能力。"}, "homepage.multiModel.advantage3.title": {"message": "完整产品访问权限"}, "homepage.multiModel.advantage3.description": {"message": "您的订阅让您获得所有FunBlocks AI产品的完整访问权限 - 包括AIFlow、AI Docs、AI Slides等。不同订阅级别仅在AI请求数量上有所区别。"}, "homepage.multiModel.benefit": {"message": "体验多个AI模型的强大能力，无需管理多个订阅的复杂性和成本。"}, "homepage.workspace.title": {"message": "FunBlocks: 全能型 AI 工作空间"}, "homepage.workspace.description": {"message": "除了AIFlow，FunBlocks还作为一个全能型AI工作空间，旨在满足您所有的工作和学习需求，提供无缝集成的写作、演示、头脑风暴、浏览和研究工具。"}, "homepage.workspace.writer.title": {"message": "AI Docs"}, "homepage.workspace.writer.description": {"message": "体验Notion式的页面与AI写作助手。创建精美的文档、笔记和内容，获得智能建议和格式化支持。"}, "homepage.workspace.slides.title": {"message": "AI Slides"}, "homepage.workspace.slides.description": {"message": "根据任何主题一键生成专业演示文稿。完全支持Markdown格式，轻松编写和编辑幻灯片内容。"}, "homepage.workspace.aiflow.title": {"message": "AIFlow"}, "homepage.workspace.aiflow.description": {"message": "通过AI驱动的白板和思维导图工具释放创造力。可视化复杂想法，借助AI进行头脑风暴，有效组织思维。"}, "homepage.workspace.extension.title": {"message": "AI Browser Extension"}, "homepage.workspace.extension.description": {"message": "增强您的浏览体验，通过智能扩展在任何网页上辅助阅读和写作。总结内容，起草回复，高效研究。"}, "homepage.useCases.title": {"message": "使用场景"}, "homepage.useCases.description": {"message": "FunBlocks AIFlow适应多种知识工作场景，增强您的思维过程，从探索到执行。"}, "homepage.useCases.learning.title": {"message": "基于主题的学习"}, "homepage.useCases.learning.description": {"message": "用思维导图构建全面的知识系统，发现跨学科的联系。将书籍、讲座和资源转化为互动的视觉知识地图。"}, "homepage.useCases.strategy.title": {"message": "工作计划策略"}, "homepage.useCases.strategy.description": {"message": "通过可视化映射目标、分解复杂计划和生成解决路径来制定战略工作计划，获得AI指导。"}, "homepage.useCases.analysis.title": {"message": "内容分析"}, "homepage.useCases.analysis.description": {"message": "通过思维导图深入分析电影、书籍和艺术作品中的叙事、主题及角色关系，提炼出深刻的洞察，构建全面的知识体系和思维框架。"}, "homepage.useCases.problem.title": {"message": "问题分析"}, "homepage.useCases.problem.description": {"message": "从多个角度可视化复杂问题，识别根本原因，并通过结构化的AI辅助探索发现创新解决方案。"}, "homepage.useCases.content.title": {"message": "内容生成"}, "homepage.useCases.content.description": {"message": "将头脑风暴的思维导图一键转化为精美文档、专业幻灯片、引人入胜的图片和信息丰富的信息图表，简化工作流程。"}, "homepage.useCases.thinking.title": {"message": "思维能力提升"}, "homepage.useCases.thinking.description": {"message": "通过AI引导的思维导图，逐步提升批判性思维和创造性思维能力。通过定期使用结构化思维框架，培养高级认知技能。"}, "homepage.testimonials.title": {"message": "用户反馈"}, "homepage.testimonials.description": {"message": "了解FunBlocks AI如何改变专业人士、学生和团队的工作、学习和创作方式。"}, "homepage.testimonials.user1.name": {"message": "<PERSON>"}, "homepage.testimonials.user1.role": {"message": "教育顾问"}, "homepage.testimonials.user1.text": {"message": "\"FunBlocks AI彻底改变了我创建教育内容的方式。思维导图功能帮助我直观地组织复杂主题，能够将这些导图直接转换为演示幻灯片，为我节省了大量工作时间。我的学生喜欢我现在制作的清晰、视觉上吸引人的材料。\""}, "homepage.testimonials.user2.name": {"message": "<PERSON>"}, "homepage.testimonials.user2.role": {"message": "产品经理"}, "homepage.testimonials.user2.text": {"message": "\"我尝试过许多生产力工具，但FunBlocks AI脱颖而出。AI驱动的思维导图与能够在几秒钟内生成专业文档的能力相结合，显著改善了我们团队的工作流程。最令人印象深刻的部分？我注意到我自己的批判性思维技能也随着定期使用而提高。\""}, "homepage.testimonials.user3.name": {"message": "<PERSON>"}, "homepage.testimonials.user3.role": {"message": "教师"}, "homepage.testimonials.user3.text": {"message": "\"作为一名教师，我的工作效率通过FunBlocks AI Flow达到了新的高度！这个令人难以置信的工具已成为我的得力助手，帮助我更深入地理解内容，并为我的学生提供更清晰、更吸引人的课程。我对它的能力印象深刻，以至于决定在下周我主持的网络研讨会上与我的同事们分享。FunBlocks AI Flow绝对是一个强大的工具——对于任何希望提升教学效果和简化工作流程的教师来说，它都是一个游戏规则的改变者！\""}, "homepage.testimonials.user4.name": {"message": "<PERSON>."}, "homepage.testimonials.user4.role": {"message": "研究生"}, "homepage.testimonials.user4.text": {"message": "\"作为一名研究生，组织复杂信息至关重要。FunBlocks AI帮助我以可视化方式构建研究内容，并生成全面的文献综述。AI辅助让我看到了我可能错过的概念之间的联系。就像有一个永不睡觉的研究伙伴！\""}, "homepage.testimonials.user5.name": {"message": "<PERSON>"}, "homepage.testimonials.user5.role": {"message": "市场经理"}, "homepage.testimonials.user5.text": {"message": "\"使用FunBlocks AIFlow进行头脑风暴帮助我突破了心理障碍，产生了突破性的想法，为我们的营销策略带来了创新解决方案。\""}, "homepage.testimonials.user6.name": {"message": "<PERSON>"}, "homepage.testimonials.user6.role": {"message": "学生"}, "homepage.testimonials.user6.text": {"message": "\"作为一名学生，FunBlocks AI显著增强了我的学习能力。它有助于主题学习，使我能够更深入地探索和研究学科，最终提高了我的批判性思维能力。\""}, "prompt_optimizer.head.title": {"message": "FunBlocks AI Prompt优化器与批判性思维助手 - 增强AI对话体验"}, "prompt_optimizer.head.description": {"message": "使用我们的AI助手为ChatGPT、<PERSON>、Gemini等优化提示词并增强批判性思维。功能包括提示词优化、批判性分析、相关问题生成和主题探索，帮助您与AI更好地思考。"}, "prompt_optimizer.head.keywords": {"message": "AI提示词优化器, 批判性思维助手, ChatGPT提示词, <PERSON>提示词, AI对话增强, 提示词工程, 批判性分析, AI助手, 浏览器扩展, 更好的AI回答"}, "prompt_optimizer.hero.badge": {"message": "全新浏览器扩展"}, "prompt_optimizer.hero.title": {"message": "AI Prompt优化器与AI回答分析器"}, "prompt_optimizer.hero.subtitle": {"message": "通过提示词优化和批判性思维工具增强您的AI对话。让AI帮助您更好地思考，而不是代替您思考。"}, "prompt_optimizer.hero.trial": {"message": "下载Chrome插件"}, "prompt_optimizer.hero.learn_more": {"message": "了解工作原理"}, "prompt_optimizer.hero.stat1": {"message": "免费优化次数"}, "prompt_optimizer.hero.stat2": {"message": "支持的AI平台"}, "prompt_optimizer.hero.stat3": {"message": "用户评分"}, "prompt_optimizer.hero.image_caption": {"message": "点击放大"}, "prompt_optimizer.benefits.title": {"message": "主要优势"}, "prompt_optimizer.benefits.benefit1.title": {"message": "更准确的回答"}, "prompt_optimizer.benefits.benefit1.description": {"message": "通过清晰传达您的意图和需求的优化提示词，从AI获得您真正需要的内容。"}, "prompt_optimizer.benefits.benefit2.title": {"message": "节省时间和精力"}, "prompt_optimizer.benefits.benefit2.description": {"message": "通过一开始就使用精心设计的提示词，消除来回澄清的需要，直接解决所有必要细节。"}, "prompt_optimizer.benefits.benefit3.title": {"message": "提升您的提示词技能"}, "prompt_optimizer.benefits.benefit3.description": {"message": "通过观察扩展如何将基本提示词转化为强大指令，从实例中学习。"}, "prompt_optimizer.benefits.benefit4.title": {"message": "批判性思维增强"}, "prompt_optimizer.benefits.benefit4.description": {"message": "通过AI驱动的分析工具培养批判性思维技能，帮助您评估回答并进行更深入的思考。"}, "prompt_optimizer.benefits.benefit7.title": {"message": "更深入的探索"}, "prompt_optimizer.benefits.benefit7.description": {"message": "发现相关问题和主题，从不同角度更全面地探索主题。"}, "prompt_optimizer.benefits.benefit5.title": {"message": "兼容您喜爱的AI工具"}, "prompt_optimizer.benefits.benefit5.description": {"message": "与ChatGPT、<PERSON>、<PERSON>、Perplexity、DeepSeek和其他流行的AI聊天应用兼容。"}, "prompt_optimizer.benefits.benefit6.title": {"message": "一键实现"}, "prompt_optimizer.benefits.benefit6.description": {"message": "只需单击一下，即可无缝替换原始提示词为优化版本。AI驱动的提示词工程，触手可及。"}, "prompt_optimizer.how_it_works.title": {"message": "Prompt优化器的工作原理"}, "prompt_optimizer.how_it_works.description": {"message": "我们的浏览器扩展无缝集成到ChatGPT、Claude和Gemini等领先AI平台中，在您需要的地方提供强大的提示词优化和批判性思维工具"}, "prompt_optimizer.how_it_works.step1.title": {"message": "安装扩展"}, "prompt_optimizer.how_it_works.step1.description": {"message": "只需几次点击即可将Prompt优化器添加到Chrome或Edge。无需复杂设置。"}, "prompt_optimizer.how_it_works.step2.title": {"message": "访问您喜爱的AI聊天"}, "prompt_optimizer.how_it_works.step2.description": {"message": "打开ChatGPT、<PERSON>、<PERSON>或任何支持的AI平台，我们的工具会自动出现。"}, "prompt_optimizer.how_it_works.step3.title": {"message": "优化您的提示词"}, "prompt_optimizer.how_it_works.step3.description": {"message": "使用我们的一键工具将基本问题转化为强大、精确的提示词，获得更好的结果。"}, "prompt_optimizer.how_it_works.feature1.badge": {"message": "功能亮点"}, "prompt_optimizer.how_it_works.feature1.title": {"message": "智能问题和指令优化"}, "prompt_optimizer.how_it_works.feature1.description": {"message": "扩展在AI聊天应用的输入框下方添加Prompt优化器小部件。我们的AI分析您的提示词，并根据经过验证的提示词工程技术提出改进建议。"}, "prompt_optimizer.how_it_works.feature1.point1": {"message": "选择\"优化问题\"，生成5个更准确、更具体或从不同角度提出的问题"}, "prompt_optimizer.how_it_works.feature1.point2": {"message": "选择\"优化指令\"，根据您的意图和核心需求澄清您的提示词"}, "prompt_optimizer.how_it_works.feature1.point3": {"message": "如果您的指令需要更多信息，将出现动态表单，帮助您提供必要的详细信息"}, "prompt_optimizer.how_it_works.feature1.point4": {"message": "只需单击一下，即可用优化版本替换原始提示词"}, "prompt_optimizer.how_it_works.feature1.image_caption": {"message": "点击放大"}, "prompt_optimizer.how_it_works.feature2.badge": {"message": "独家功能"}, "prompt_optimizer.how_it_works.feature2.title": {"message": "批判性思维助手"}, "prompt_optimizer.how_it_works.feature2.description": {"message": "我们全新的批判性思维助手下拉菜单提供强大的工具，帮助您更深入地思考AI回答，并从多个角度探索主题。"}, "prompt_optimizer.how_it_works.feature2.point1": {"message": "在每个AI回答的工具栏中访问\"批判性思维助手\"下拉菜单"}, "prompt_optimizer.how_it_works.feature2.point2": {"message": "使用\"批判性分析\"功能评估AI回答的逻辑一致性、潜在偏见和替代观点"}, "prompt_optimizer.how_it_works.feature2.point3": {"message": "生成\"相关问题\"来探索您可能未曾考虑过的主题深层方面"}, "prompt_optimizer.how_it_works.feature2.point4": {"message": "发现\"相关主题\"来拓宽您的理解并探索相关联的学科"}, "prompt_optimizer.how_it_works.feature2.image_caption": {"message": "点击放大"}, "prompt_optimizer.how_it_works.critical_analysis.badge": {"message": "批判性分析"}, "prompt_optimizer.how_it_works.critical_analysis.title": {"message": "用批判性思维评估AI回答"}, "prompt_optimizer.how_it_works.critical_analysis.description": {"message": "我们的批判性分析功能帮助您更深入地思考AI回答，评估其逻辑一致性、潜在偏见和替代观点。"}, "prompt_optimizer.how_it_works.critical_analysis.point1": {"message": "分析AI回答的逻辑一致性和事实准确性"}, "prompt_optimizer.how_it_works.critical_analysis.point2": {"message": "识别AI生成内容中的潜在偏见和局限性"}, "prompt_optimizer.how_it_works.critical_analysis.point3": {"message": "探索替代观点和反驳论据"}, "prompt_optimizer.how_it_works.critical_analysis.point4": {"message": "培养更强的批判性思维技能，实现更好的AI协作"}, "prompt_optimizer.how_it_works.critical_analysis.image_caption": {"message": "点击放大"}, "prompt_optimizer.how_it_works.feature4.badge": {"message": "无限探索"}, "prompt_optimizer.how_it_works.feature4.title": {"message": "AI生成探索空间"}, "prompt_optimizer.how_it_works.feature4.description": {"message": "我们独特的探索工具帮助您发现可能未曾考虑过的相关问题和主题。"}, "prompt_optimizer.how_it_works.feature4.point1": {"message": "通过后续问题深入探索见解"}, "prompt_optimizer.how_it_works.feature4.point2": {"message": "通过相关主题发现更广阔的视角"}, "prompt_optimizer.how_it_works.feature5.badge": {"message": "优化提问"}, "prompt_optimizer.how_it_works.feature5.title": {"message": "好问题是成功的一半"}, "prompt_optimizer.how_it_works.feature5.description": {"message": "更好的输入带来更好的输出。我们的工具引导你撰写更清晰的提示词，让 AI 给出更明确、可信的结果。"}, "prompt_optimizer.how_it_works.feature5.point1": {"message": "AI 帮助你重新表述问题，使其更清晰、更有效"}, "prompt_optimizer.how_it_works.feature5.point2": {"message": "优化后的提示词能带来更准确、更相关的 AI 回应"}, "prompt_optimizer.how_it_works.feature5.point3": {"message": "全面提升写作、编程、研究等任务的成果"}, "prompt_optimizer.how_it_works.feature5.point4": {"message": "通过不断优化提示词，提升你的提问能力"}, "prompt_optimizer.how_it_works.cta.title": {"message": "超越提示词工程"}, "prompt_optimizer.how_it_works.cta.description": {"message": "好的回答始于好的问题。FunBlocks AI Prompt Optimizer 超越模板化提示词 — 它优化你的问题，发现更多相关问题、相关主题，帮助你获得最深刻的回答。"}, "prompt_optimizer.how_it_works.cta.button": {"message": "下载Chrome插件"}, "prompt_optimizer.why_it_matters.title": {"message": "为什么好的提示词很重要"}, "prompt_optimizer.why_it_matters.description": {"message": "在生成式AI时代，提出好问题的能力是一项关键技能"}, "prompt_optimizer.why_it_matters.point1.title": {"message": "获得更好回答的关键"}, "prompt_optimizer.why_it_matters.point1.description": {"message": "AI模型拥有广泛的知识和强大的能力，但它们需要清晰、具体的指令才能发挥最佳效果。好的提示词能够释放它们的全部潜力。"}, "prompt_optimizer.why_it_matters.point2.title": {"message": "关键的人类技能"}, "prompt_optimizer.why_it_matters.point2.description": {"message": "随着AI越来越融入我们的工作和生活，提出好问题和提供清晰指令的能力变得越来越有价值。"}, "prompt_optimizer.why_it_matters.point3.title": {"message": "竞争优势"}, "prompt_optimizer.why_it_matters.point3.description": {"message": "那些能够有效与AI沟通的人在AI驱动的未来中，在生产力、创造力和解决问题方面将拥有显著优势。"}, "prompt_optimizer.why_thinking_matters.title": {"message": "为什么在生成式AI时代思维能力至关重要"}, "prompt_optimizer.why_thinking_matters.description": {"message": "随着AI变得更加强大，人类的思维技能变得更加宝贵，而不是相反。未来属于那些能够批判性思考并与AI有效协作的人。"}, "prompt_optimizer.why_thinking_matters.point1.title": {"message": "AI放大人类智能"}, "prompt_optimizer.why_thinking_matters.point1.description": {"message": "AI不会取代人类思维——它会放大人类思维。您的思维技能越好，就越能有效地利用AI的能力来解决复杂问题并产生创新解决方案。"}, "prompt_optimizer.why_thinking_matters.point2.title": {"message": "批判性分析至关重要"}, "prompt_optimizer.why_thinking_matters.point2.description": {"message": "AI可以生成大量信息，但人类必须评估其准确性、相关性和影响。批判性思维技能帮助您从噪音中识别出高质量的见解。"}, "prompt_optimizer.why_thinking_matters.point3.title": {"message": "创造性问题解决"}, "prompt_optimizer.why_thinking_matters.point3.description": {"message": "虽然AI擅长模式识别和数据处理，但人类擅长创造性思维、伦理推理和理解上下文。这些独特的人类技能在AI驱动的世界中变得更加宝贵。"}, "prompt_optimizer.why_thinking_matters.point4.title": {"message": "人机协作"}, "prompt_optimizer.why_thinking_matters.point4.description": {"message": "工作的未来是关于人机协作。那些能够战略性地思考如何将人类创造力与AI效率相结合的人将在各自领域中领先。"}, "prompt_optimizer.why_thinking_matters.cta.title": {"message": "与AI一起培养您的思维技能"}, "prompt_optimizer.why_thinking_matters.cta.description": {"message": "我们的批判性思维助手不仅仅优化提示词——它帮助您培养在AI时代至关重要的思维技能。"}, "prompt_optimizer.why_thinking_matters.cta.button": {"message": "开始与AI更好地思考"}, "prompt_optimizer.comparison.title": {"message": "FunBlocks AI Prompt优化器的优势对比"}, "prompt_optimizer.comparison.description": {"message": "了解我们的扩展如何在标准AI交互和其他解决方案中脱颖而出"}, "prompt_optimizer.comparison.note": {"message": "FunBlocks AI Prompt优化器将AI辅助的优势与实用工具相结合，帮助您随着时间的推移培养更好的提示词技能。"}, "prompt_optimizer.comparison.funblocksHeader": {"message": "FunBlocks AI Prompt优化器"}, "prompt_optimizer.comparison.chatgptHeader": {"message": "标准ChatGPT"}, "prompt_optimizer.comparison.promptEngineeringHeader": {"message": "手动提示词工程"}, "prompt_optimizer.comparison.otherExtensionsHeader": {"message": "其他AI扩展"}, "prompt_optimizer.comparison.feature1": {"message": "一键提示词优化"}, "prompt_optimizer.comparison.feature2": {"message": "缺失信息的动态表单"}, "prompt_optimizer.comparison.feature3": {"message": "相关问题生成"}, "prompt_optimizer.comparison.feature4": {"message": "相关主题探索"}, "prompt_optimizer.comparison.feature5": {"message": "多平台支持"}, "prompt_optimizer.comparison.feature6": {"message": "提升提示词技能"}, "prompt_optimizer.comparison.feature7": {"message": "每日免费使用"}, "prompt_optimizer.video.title": {"message": "观看Prompt优化器实际演示"}, "prompt_optimizer.video.description": {"message": "了解FunBlocks AI Prompt优化器如何改变您的AI互动体验"}, "prompt_optimizer.video.cta": {"message": "亲自体验"}, "prompt_optimizer.video.feature1.title": {"message": "实际操作演示"}, "prompt_optimizer.video.feature1.description": {"message": "观看扩展如何无缝集成到AI聊天应用中"}, "prompt_optimizer.video.feature2.title": {"message": "学习最佳实践"}, "prompt_optimizer.video.feature2.description": {"message": "发现您可以自己应用的有效提示词优化技巧"}, "prompt_optimizer.video.feature3.title": {"message": "探索使用场景"}, "prompt_optimizer.video.feature3.description": {"message": "查看优化提示词如何改善AI回答的真实案例"}, "prompt_optimizer.pricing.title": {"message": "免费试用"}, "prompt_optimizer.pricing.description": {"message": "通过我们慷慨的免费试用和灵活的订阅选项开始使用"}, "prompt_optimizer.pricing.free_trial.title": {"message": "免费试用"}, "prompt_optimizer.pricing.free_trial.feature1": {"message": "新用户30次免费优化"}, "prompt_optimizer.pricing.free_trial.feature2": {"message": "每日10次免费优化"}, "prompt_optimizer.pricing.free_trial.feature3": {"message": "访问所有核心功能"}, "prompt_optimizer.pricing.free_trial.note": {"message": "无需信用卡"}, "prompt_optimizer.pricing.subscription.title": {"message": "FunBlocks AI套餐"}, "prompt_optimizer.pricing.subscription.feature1": {"message": "包含在所有FunBlocks AI订阅计划中"}, "prompt_optimizer.pricing.subscription.feature2": {"message": "选择适合您需求的计划"}, "prompt_optimizer.pricing.subscription.feature3": {"message": "访问整个FunBlocks AI生态系统"}, "prompt_optimizer.pricing.subscription.note": {"message": "详情请参阅价格页面"}, "prompt_optimizer.testimonials.title": {"message": "用户反馈"}, "prompt_optimizer.testimonials.description": {"message": "了解FunBlocks AI Prompt优化器如何改变人们与AI互动的方式"}, "prompt_optimizer.testimonials.user1.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user1.role": {"message": "内容创作者"}, "prompt_optimizer.testimonials.user1.text": {"message": "\"这个扩展彻底改变了我与AI合作的方式。我曾经很难让ChatGPT准确理解我的需求，但现在提示词优化功能帮助我澄清我的请求。相关问题功能还帮助我探索了我自己不会想到的主题。\""}, "prompt_optimizer.testimonials.user2.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user2.role": {"message": "研究员"}, "prompt_optimizer.testimonials.user2.text": {"message": "\"作为每天使用AI工具进行研究的人，优化提示词的能力非常宝贵。我获得了更精确的信息，花更少的时间完善我的问题。相关主题功能还帮助我发现了我可能错过的联系。\""}, "prompt_optimizer.testimonials.user3.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user3.role": {"message": "学生"}, "prompt_optimizer.testimonials.user3.text": {"message": "\"自从开始使用这个扩展以来，我注意到我自己的提问技能有所提高。通过看到它如何将我的基本问题转化为更具体的问题，我学会了更批判性地思考我真正想要问的是什么。这就像有一个个人导师教我提出更好的问题！\""}, "prompt_optimizer.testimonials.user4.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user4.role": {"message": "软件开发者"}, "prompt_optimizer.testimonials.user4.text": {"message": "\"作为一名开发者，我依赖AI工具进行文档编写、调试和学习新框架。Prompt优化器通过帮助我制定精确的技术问题，显著改善了我的工作流程。当我需要提供代码上下文时，动态表单功能特别有用——它确保我包含所有必要的细节以获得准确的回答。\""}, "prompt_optimizer.testimonials.user5.name": {"message": "<PERSON>"}, "prompt_optimizer.testimonials.user5.role": {"message": "数字营销专家"}, "prompt_optimizer.testimonials.user5.text": {"message": "\"这个扩展已成为我内容创作过程中不可或缺的工具。在头脑风暴创意或研究主题时，相关问题功能帮助我探索我本不会考虑的角度。我在更短的时间内创建更全面、研究更充分的内容，我的客户也注意到了质量上的差异。\""}, "prompt_optimizer.testimonials.user6.name": {"message": "Dr. <PERSON>"}, "prompt_optimizer.testimonials.user6.role": {"message": "医疗保健专业人士"}, "prompt_optimizer.testimonials.user6.text": {"message": "\"在医疗保健领域，精确性至关重要。这个扩展帮助我在研究医学主题或治疗选项时制定清晰、具体的查询。在不同AI平台上优化提示词的能力非常宝贵，因为我可以选择最适合特定类型医学信息的模型，同时保持一致、高质量的互动。\""}, "prompt_optimizer.cta.title": {"message": "准备好提升您的AI对话体验了吗？"}, "prompt_optimizer.cta.description": {"message": "立即下载我们的Chrome插件，开始获得更好的AI响应"}, "prompt_optimizer.cta.button": {"message": "下载浏览器插件"}, "prompt_optimizer.browser_not_supported": {"message": "该扩展仅支持Chrome和Edge浏览器。请使用Chrome或新版Edge安装扩展。"}, "prompt_optimizer.faq.q1": {"message": "Prompt优化器扩展支持哪些AI平台？"}, "prompt_optimizer.faq.a1": {"message": "该扩展目前支持ChatGPT、<PERSON>、Gemini、Perplexity、DeepSeek和其他流行的AI聊天应用。我们不断添加对更多平台的支持。"}, "prompt_optimizer.faq.q2": {"message": "免费试用如何运作？"}, "prompt_optimizer.faq.a2": {"message": "新用户首次安装扩展时会获得30次免费优化。此外，所有用户每天都会获得10次免费优化，无论订阅状态如何。"}, "prompt_optimizer.faq.q3": {"message": "使用扩展是否需要创建FunBlocks账户？"}, "prompt_optimizer.faq.a3": {"message": "是的，注册FunBlocks账户（免费）可以让您访问新用户奖励，并且可以跨设备使用。"}, "prompt_optimizer.faq.q4": {"message": "扩展如何提升我的提示词技能？"}, "prompt_optimizer.faq.a4": {"message": "通过向您展示优化版本的提示词，您将学习有效的提示模式和技巧。随着时间的推移，您将自然地将这些改进融入到自己的提示风格中，变得更有效地与AI沟通。"}, "prompt_optimizer.faq.q5": {"message": "使用扩展时我的数据安全吗？"}, "prompt_optimizer.faq.a5": {"message": "是的。我们非常重视隐私。扩展只处理您选择优化的特定提示词。我们不存储您的对话历史或与第三方共享您的数据。所有处理都通过我们的API安全完成。"}, "prompt_optimizer.faq.q6": {"message": "这与直接使用ChatGPT改进我的提示词有何不同？"}, "prompt_optimizer.faq.a6": {"message": "虽然您可以要求ChatGPT帮助改进您的提示词，但我们的扩展提供了几个优势：1) 它直接集成到界面中，节省您的时间和额外步骤，2) 它专为提示词优化而设计，具有专门的算法，3) 它提供额外功能，如相关问题和主题探索，4) 它适用于多个AI平台，而不仅仅是ChatGPT。"}, "prompt_optimizer.faq.q7": {"message": "扩展如何提高用户的思维能力？"}, "prompt_optimizer.faq.a7": {"message": "扩展的主要功能旨在提高用户的思维和问题解决能力。通过AI优化的提示词，用户可以获得更好的问题，提高提问能力；同时，AI生成的相关主题和问题也能扩展用户对特定主题或问题的多角度思考能力。最后，AI优化的提示词还能帮助用户更好地描述自己的需求。"}, "prompt_optimizer.faq.q8": {"message": "缺失信息的动态表单功能是如何工作的？"}, "prompt_optimizer.faq.a8": {"message": "当您选择\"优化指令\"时，我们的AI会分析您的提示词，识别对高质量回答至关重要但缺失的信息。如果检测到信息缺口，扩展会生成一个自定义表单，其中包含针对缺失细节的特定字段。完成表单后，这些信息会无缝集成到您优化后的提示词中，确保AI拥有提供准确和全面回答所需的一切。"}, "prompt_optimizer.faq.q9": {"message": "扩展可以离线工作还是需要互联网连接？"}, "prompt_optimizer.faq.a9": {"message": "Prompt优化器扩展需要互联网连接才能运行，因为它与我们的优化服务器通信。这确保您始终能够访问最新的优化算法和功能。只要您登录到FunBlocks账户，无论使用哪台设备，您的优化次数都会计入您的每日或订阅配额。"}, "prompt_optimizer.faq.q10": {"message": "什么是懒惰提示？"}, "prompt_optimizer.faq.a10": {"message": "懒惰提示是一种方法，首先向大型语言模型（LLM）提供快速、不精确的提示，然后评估输出质量，最后根据需要提供更多上下文。它的目的是探索在仍能获得有用结果的情况下，能省略多少上下文。同时，LLM可能会展现你从未想到的问题或解决方案。"}, "prompt_optimizer.faq.q10e": {"message": "FunBlocks AI Prompt Optimizer 是否支持懒惰提示？"}, "prompt_optimizer.faq.a10e": {"message": "FunBlocks AI Prompt Optimizer是实践懒惰提示的理想工具。用户只需提出简洁的想法或问题，然后点击FunBlocks AI Prompt Optimizer按钮，系统将一键完成对提示的拓展和探索，帮助用户发现更多可能性和未曾想到的方向和解决方案。"}, "prompt_optimizer.faq.q11": {"message": "Prompt优化器扩展如何处理专业技术或特定领域的术语？"}, "prompt_optimizer.faq.a11": {"message": "我们的扩展设计用于保留和正确语境化各种领域的专业术语，包括编程、医学、法律、金融和科学研究。在优化包含技术术语的提示词时，系统会保持精确的术语，同时增强周围上下文的结构和清晰度。对于高度专业化的领域，动态表单功能可能会请求额外的上下文以确保准确的优化。"}, "prompt_optimizer.faq.q12": {"message": "扩展的核心价值是什么？"}, "prompt_optimizer.faq.a12": {"message": "我们开发这个产品是为了让您更好地与AI沟通，提高AI生成的质量和回答。同时，使用FunBlocks AI Prompt Optimizer的过程中，用户还可以提升高阶思维能力，如提问能力和多角度思考。"}, "homepage.cta.title": {"message": "准备好开始知识冒险了吗？"}, "homepage.cta.subtitle": {"message": "加入FunBlocks AI，释放您无限的认知潜力！"}, "homepage.cta.button": {"message": "立即开始您的免费试用"}, "homepage.faq.title": {"message": "常见问题"}, "homepage.faq.q0": {"message": "什么是FunBlocks？"}, "homepage.faq.a0": {"message": "FunBlocks是一个AI驱动的知识学习与生产力平台，提供一站式AI工作空间。用户可以利用AI助手撰写文档和笔记，进行思维导图和白板头脑风暴，进行主题学习，还能以Markdown格式创建幻灯片，也能根据上下文一键生成信息图。此外，FunBlocks还提供浏览器插件，帮助用户在任何网页上进行AI辅助的写作和阅读。"}, "homepage.faq.q01": {"message": "为什么主页着重介绍FunBlocks AIFlow？"}, "homepage.faq.a01": {"message": "主页强调FunBlocks AIFlow，因为它展示了该平台在提升学习、创造力和生产力方面的强大功能。FunBlocks AIFlow使用可视化的AI白板和思维导图来激发创造力，并允许用户与AI进行头脑风暴。用户只需点击一下，就可以将思维导图转化为实用的输出，如幻灯片和提案，这些内容会自动保存在FunBlocks工作区中，便于编辑。这种设置鼓励用户探索其他FunBlocks产品，如FunBlocks AI Docs和FunBlocks AI Slides。"}, "homepage.faq.q1": {"message": "FunBlocks AIFlow是什么？"}, "homepage.faq.a1": {"message": "FunBlocks AIFlow是一个AI驱动的视觉思维平台，旨在帮助用户进行头脑风暴和生成创新想法。它能够将复杂的概念转化为思维导图、幻灯片和信息图表，结合AI的强大功能与视觉思维技术，显著提升您的认知能力和生产力。"}, "homepage.faq.q2": {"message": "AIFlow与ChatGPT有什么不同？"}, "homepage.faq.a2": {"message": "ChatGPT提供线性文本对话，而AIFlow提供视觉化、多维度的工作空间。您可以同时在多个方向探索想法，通过视觉层次保持上下文，并即时将您的想法转化为专业交付物。"}, "homepage.faq.q3": {"message": "我可以用AIFlow创建什么？"}, "homepage.faq.a3": {"message": "使用AIFlow，您可以创建思维导图、演示幻灯片、信息图表、战略规划、知识地图和复杂主题的视觉分析。平台支持各种思维框架，并可以将您的视觉作品转换为不同格式。"}, "homepage.faq.q4": {"message": "除了AIFlow，FunBlocks还有哪些产品？"}, "homepage.faq.a4": {"message": "FunBlocks提供了一站式AI工具集和工作平台，旨在提升生产力、激发创造力。它集成了 AI 赋能的思维导图、文档编写和幻灯片生成功能，打造全方位知识工作空间。\n\n主要产品包括:\nFunBlocks AI Flow: AI 驱动的思维导图工具，用于激发创造力、探索知识。\nFunBlocks AI Docs: AI 驱动的 Notion 风格块编辑器，提供 AI 写作助手，辅助文档编写。\nFunBlocks AI Slides: AI 驱动的幻灯片生成工具，一键创建幻灯片，提供内容撰写和演示指导。\nFunBlocks 浏览器扩展: 提升效率的浏览器扩展，支持 AI 写作、阅读、邮件回复等功能。\nFunBlocks AI Tools: 即时访问的专业 AI 工具，针对特定用例提供解决方案，包括 AI 图形、AI 思维导图、AI 集思广益、AI 幻灯片、AI 决策分析器、AI 任务计划器、AI YouTube 摘要器等。\nAI Assistant: 可定制的 AI 助手，支持自定义提示，集成 AI Flow、AI Slides、AI Docs 和浏览器扩展。"}, "homepage.faq.q5": {"message": "FunBlocks适合团队协作吗？"}, "homepage.faq.a5": {"message": "是的，FunBlocks既适合个人使用也适合团队使用。团队可以在共享的工作空间上协作，一起头脑风暴，文档写作等，并在AI的协助下维护共享知识库。"}, "homepage.faq.q6": {"message": "使用FunBlocks AI需要技术技能吗？"}, "homepage.faq.a6": {"message": "不需要技术技能。FunBlocks AI具有直观的界面，让您轻松开始创建白板或文档，并使用AI助手进行学习和创作。"}, "homepage.faq.q7": {"message": "哪些行业或角色可以从FunBlocks AI中受益？"}, "homepage.faq.a7": {"message": "FunBlocks AI对教育工作者、学生、商业专业人士、研究人员、作家以及任何从事知识工作的人都很有价值。它特别适用于战略规划、内容创作、学习、问题解决和决策制定。"}, "homepage.faq.q8": {"message": "AIFlow是又一个AI Mindmap工具吗？和普通AI MindMap工具有什么区别？"}, "homepage.faq.q9": {"message": "在帮助思考方面，AIFlow有什么优势？"}, "homepage.faq.a9": {"message": "AIFlow在辅助思考方面具有多重优势：\n1. 多维度分析：通过思维导图和AI建议，帮助您从多个角度审视问题。\n2. 突破思维局限：AI可以提供意想不到的见解，帮助您跳出固有思维模式。\n3. 知识关联：AI可以帮助您在不同领域之间建立联系，促进创新思维。\n4. 经典思维模型运用：AIFlow支持多种经典思维模型，帮助你以清晰和高效的思维框架进行思考。\n5. 问题分解：帮助您将复杂问题分解为可管理的小部分，便于逐步解决。\n6. 知识探索：输入一个主题，AI可以引导您探索相关领域，拓展知识面。\n这些特性使AIFlow成为一个强大的思考辅助工具，能够显著增强您的分析和创新能力。"}, "homepage.faq.q10": {"message": "使用FunBlocks AIFlow进行头脑风暴有什么优势？"}, "homepage.faq.a10": {"message": "FunBlocks AIFlow利用AI技术简化头脑风暴过程，能够快速生成全面的思维导图，帮助你轻松获取丰富的创意和想法。其灵活的交互界面让你可以通过AI助手从不同角度探索更多思路。此外，AIFlow结合了经典思维模型（如六顶思考帽、麦肯锡方法、SCAMPER、SWOT分析、转换视角、逆向思维、商业模型画布和价值主张画布），充分发挥了大语言模型的能力，助你获得新颖的创意。"}, "homepage.faq.q11": {"message": "FunBlocks AI的付费会员是否适用于所有FunBlocks AI产品和服务？"}, "homepage.faq.a11": {"message": "是的，除非另有说明，FunBlocks AI的付费会员享有的权限和AI访问额度适用于平台上的所有产品和服务。"}, "homepage.faq.q12": {"message": "如果我使用自己的API Key（如ChatGPT、Claude、Gemini、Groq等），还需要为FunBlocks AI服务支付费用吗？"}, "homepage.faq.a12": {"message": "不需要。如果您使用自己的API Key，您只需向服务提供商支付费用。FunBlocks AI不会收取额外费用，也不受每日免费访问配额的限制。"}, "homepage.faq.q14": {"message": "FunBlocks AIFlow适合什么类型的创意工作？"}, "homepage.faq.a14": {"message": "FunBlocks AIFlow非常适合需要多角度思考和创意探索的任务，如头脑风暴、主题研究和内容策划。它结合了AI的强大理解力和推理能力，在无限画布空间中进行多线程互动，帮助用户深入探索并拓展思维。"}, "homepage.faq.q15": {"message": "为什么FunBlocks AI支持所有主流大语言模型？"}, "homepage.faq.a15": {"message": "FunBlocks AI致力于为用户提供灵活的选择和最佳体验。通过支持所有主流大语言模型，我们满足您的不同需求，确保您可以根据个人偏好和工作流程灵活选择最合适的AI模型，从而提升工作效率和创造力。"}, "homepage.faq.q16": {"message": "可以免费试用FunBlocks AI吗？"}, "homepage.faq.a16": {"message": "是的，FunBlocks AI提供免费试用。新用户注册后将获得30次免费AI服务访问，并每天额外赠送10次免费访问，让您充分体验FunBlocks AI在学习和工作中的变革与帮助。此外，FunBlocks AI还设有奖励机制，邀请好友注册后，您和好友均可获得额外的免费使用额度。"}, "homepage.faq.q17": {"message": "如何通过邀请好友获得免费使用额度奖励呢？"}, "homepage.faq.a17": {"message": "您可以通过两种方式邀请好友。第一，分享邀请链接，好友通过链接注册后即可获得奖励。第二，分享FunBlocks AI浏览器插件或AI Flow生成的内容，用户点击阅读后注册，您也将获得邀请奖励。"}, "homepage.faq.q18": {"message": "如何取消订阅的Plan？"}, "homepage.faq.a18": {"message": "您可以在设置中找到\"升级 AI 会员\"和\"升级 FunBlocks\"栏目。如果您订阅了某个会员计划，在相应的介绍栏中会有Cancel按钮，点击即可退订。"}, "homepage.faq.q19": {"message": "我在FunBlocks平台上的数据安全吗？"}, "homepage.faq.a19": {"message": "我们优先考虑数据安全和隐私。所有数据都经过加密，我们遵循行业最佳实践来保护数据。您可以完全控制您的内容并管理共享权限。"}, "aiflow.head.title": {"message": "与AI的可视化聊天。最适合头脑风暴、问题解决、批判性和创造性思维"}, "aiflow.head.description": {"message": "超越简单的文本聊天 - 使用GPT和Claude等先进模型在无边界画布上可视化想法和AI洞察。轻松分解复杂问题，使用结构化方法（SWOT、六顶思考帽）进行头脑风暴，从书籍、视频或网页即时生成思维导图，并创建可直接使用的演示文稿和文档输出。非常适合学习、战略规划、内容创作和深入分析。增强您的思维能力，深入探索主题，与您的智能AI伙伴一起实现更多目标。立即使用FunBlocks AIFlow提升创造力和生产力！"}, "aiflow.nav.home": {"message": "FunBlocks AIFlow"}, "aiflow.nav.intro": {"message": "AIFlow介绍"}, "aiflow.nav.features": {"message": "核心功能"}, "aiflow.nav.ai-powered-brainstorming": {"message": "AI头脑风暴"}, "aiflow.nav.explore-with-ai": {"message": "AI探索"}, "aiflow.nav.use-cases": {"message": "使用场景"}, "aiflow.nav.faq": {"message": "常见问题"}, "aiflow.nav.pricing": {"message": "定价"}, "aiflow.nav.cta": {"message": "登录"}, "aiflow.masthead.title_2": {"message": "与AI互动的新方式"}, "aiflow.masthead.subtitle": {"message": "超越 AI 聊天。将聊天框和文本线程转换为无限画布。使用集成的头脑风暴、思维导图、批判性思维和创造性思维工具，可视化想法，解决问题，更快地学习"}, "aiflow.masthead.cta": {"message": "开始免费试用"}, "aiflow.masthead.learn_more": {"message": "探索功能"}, "aiflow.intro.title": {"message": "超越ChatGPT"}, "aiflow.intro.description": {"message": "探索与AI互动的创新方式，超越文本的限制。以增强认知过程的方式可视化您的想法和AI输出。"}, "aiflow.intro.point1.name": {"message": "无边界画布"}, "aiflow.intro.point1.description": {"message": "摆脱传统对话限制，在无限空间中进行头脑风暴"}, "aiflow.intro.point2.name": {"message": "多维思维"}, "aiflow.intro.point2.description": {"message": "通过多维思维导图全面分析问题"}, "aiflow.intro.point3.name": {"message": "先进的AI助手"}, "aiflow.intro.point3.description": {"message": "集成GPT-4o和Claude-3.7等先进AI提供智能支持"}, "aiflow.intro.point4.name": {"message": "多样化应用"}, "aiflow.intro.point4.description": {"message": "AI助手助力学习、工作和创意项目"}, "aiflow.features.title": {"message": "主要功能"}, "aiflow.features.item1.name": {"message": "分解复杂问题"}, "aiflow.features.item1.description": {"message": "快速分解信息，轻松开始研究"}, "aiflow.features.item1.li1": {"message": "将复杂问题分解为可管理的部分"}, "aiflow.features.item1.li2": {"message": "快速识别关键要素和潜在挑战"}, "aiflow.features.item1.li3": {"message": "通过分而治之的方法简化研究过程"}, "aiflow.features.item2.name": {"message": "拓展思维"}, "aiflow.features.item2.description": {"message": "发现新视角，突破思维障碍"}, "aiflow.features.item2.li1": {"message": "对任何主题生成多种视角"}, "aiflow.features.item2.li2": {"message": "让AI建议意想不到的角度和方法，激发创新思维和解决方案"}, "aiflow.features.item2.li3": {"message": "发现可能错过的联系和洞察，拓宽思维"}, "aiflow.features.item2.li4": {"message": "深入探讨相关概念和领域"}, "aiflow.features.item3.name": {"message": "清晰的指令，提升生成结果的质量"}, "aiflow.features.item3.description": {"message": "AIFlow通过多种创新功能，使人与AI之间的沟通更加清晰和高效"}, "aiflow.features.item3.li1": {"message": "不擅长提问？让AI帮助你分析并优化问题"}, "aiflow.features.item3.li2": {"message": "需求描述不明确？AI会分析你的指令并生成确认表单供你确认"}, "aiflow.features.item3.li3": {"message": "无法准确描述关于图片的艺术词汇？AIFlow根据你的简单描述生成专业的艺术描述，帮助你创建图像的提示语"}, "aiflow.features.item4.name": {"message": "多样化内容支持"}, "aiflow.features.item4.description": {"message": "支持图像、笔记、任务列表、链接等，每个都有专属的AI助手"}, "aiflow.features.item4.li1": {"message": "图像：AI对视觉内容的分析，从艺术到白板"}, "aiflow.features.item4.li2": {"message": "笔记：快速捕捉想法并进行AI驱动的扩展"}, "aiflow.features.item4.li3": {"message": "任务列表：AI增强的任务管理和优先级排序"}, "aiflow.features.item4.li4": {"message": "链接：用于研究和灵感的网页内容集成"}, "aiflow.features.item5.name": {"message": "分组节点"}, "aiflow.features.item5.description": {"message": "不仅仅是一个新节点，而是更多应用场景的入口。"}, "aiflow.features.item5.li1": {"message": "将相关概念组织成有机集群，以增强理解"}, "aiflow.features.item5.li2": {"message": "利用AI助手的支持进行分析和生成"}, "aiflow.features.item5.li3": {"message": "一键合成多样化的想法，形成全面的解决方案"}, "aiflow.features.item6.name": {"message": "一体化AI助手"}, "aiflow.features.item6.description": {"message": "多种AI功能随时可用，支持可定制的AI提示。"}, "aiflow.features.item6.li1": {"message": "深入分析需求，提供有见地的建议，显著提升思维的深度和广度"}, "aiflow.features.item6.li2": {"message": "自动生成高质量内容，节省时间和精力"}, "aiflow.features.item6.li3": {"message": "支持个性化的AI指令，以满足特定需求"}, "aiflow.ai-powered-brainstorming.title": {"message": "AI增强的头脑风暴"}, "aiflow.ai-powered-brainstorming.subtitle": {"message": "通过AI驱动的经典思维模型和有序框架释放创造力"}, "aiflow.ai-powered-brainstorming.classic_models.title": {"message": "使用经典思维模型进行创意发想"}, "aiflow.ai-powered-brainstorming.classic_models.subtitle": {"message": "利用AI进行结构化的头脑风暴"}, "aiflow.ai-powered-brainstorming.classic_models.m1": {"message": "六顶思考帽"}, "aiflow.ai-powered-brainstorming.classic_models.m2": {"message": "SWOT分析"}, "aiflow.ai-powered-brainstorming.classic_models.m3": {"message": "麦肯锡方法"}, "aiflow.ai-powered-brainstorming.classic_models.m4": {"message": "第一性原则 ..."}, "aiflow.ai-powered-brainstorming.oneclick_generation.title": {"message": "即时工作成果"}, "aiflow.ai-powered-brainstorming.oneclick_generation.subtitle": {"message": "直接从头脑风暴思维导图生成可用的工作成果"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m1": {"message": "演示文稿"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m2": {"message": "解决方案文档"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m3": {"message": "信息图"}, "aiflow.ai-powered-brainstorming.oneclick_generation.m4": {"message": "图片 ..."}, "aiflow.book-insights.title": {"message": "通过AI快速生成书籍、电影等的思维导图"}, "aiflow.book-insights.subtitle": {"message": "轻松生成思维导图，提升理解力和洞察力"}, "aiflow.book-insights.features.title": {"message": "高效获取各种媒体的洞察"}, "aiflow.book-insights.features.li1": {"message": "即时从书名、电影名、艺术作品、图片和YouTube视频生成思维导图。"}, "aiflow.book-insights.features.li2": {"message": "通过AI驱动的可视化，提升对作品的欣赏和理解。"}, "aiflow.book-insights.features.li3": {"message": "发现有价值的见解和内容，丰富你的学习体验。"}, "aiflow.book-insights.features.li4": {"message": "促进高效阅读和学习，深入理解材料。"}, "aiflow.book-insights.features.li5": {"message": "通过结构化分析和探索，拓宽思维的深度和广度。"}, "aiflow.explore-with-ai.title": {"message": "与AI一起探索世界"}, "aiflow.explore-with-ai.description": {"message": "为每个主题开启更广阔的认知视野，探索无限的可能性"}, "aiflow.explore-with-ai.point1.name": {"message": "拓展探索空间"}, "aiflow.explore-with-ai.point1.description": {"message": "为您创建全面的探索空间，支持您对任何主题进行深入研究或广泛扩展"}, "aiflow.explore-with-ai.point2.name": {"message": "全面解析主题"}, "aiflow.explore-with-ai.point2.description": {"message": "针对特定主题，不仅提供准确的答案，还展示相关知识和整体视角"}, "aiflow.explore-with-ai.point3.name": {"message": "推荐相关主题"}, "aiflow.explore-with-ai.point3.description": {"message": "除了回答当前问题，AIFlow还智能生成后续问题或话题，引导您深入未知领域"}, "aiflow.explore-with-ai.point4.name": {"message": "共同探索旅程"}, "aiflow.explore-with-ai.point4.description": {"message": "与您协作探索，激发灵感，推动思维不断前进"}, "aiflow.use-cases.title": {"message": "实际应用"}, "aiflow.use-cases.description": {"message": "AIFlow 旨在适应各种知识工作场景，简化您的思维过程，从初步探索到最终执行。"}, "aiflow.use-cases.case1.title": {"message": "主题学习"}, "aiflow.use-cases.case1.description": {"message": "创建详细的知识框架，揭示不同领域之间的联系。"}, "aiflow.use-cases.case2.title": {"message": "战略工作规划"}, "aiflow.use-cases.case2.description": {"message": "生成创意，分解任务，并为复杂挑战制定解决方案。"}, "aiflow.use-cases.case3.title": {"message": "深入内容分析"}, "aiflow.use-cases.case3.description": {"message": "使用思维导图技术深入分析电影、书籍和艺术作品。"}, "aiflow.use-cases.case4.title": {"message": "全面问题分析"}, "aiflow.use-cases.case4.description": {"message": "从不同角度可视化复杂问题，以寻找创新解决方案。"}, "aiflow.use-cases.case5.title": {"message": "内容创作"}, "aiflow.use-cases.case5.description": {"message": "将头脑风暴的思维导图转化为结构良好的文档和演示文稿。"}, "aiflow.use-cases.case6.title": {"message": "提升思维能力"}, "aiflow.use-cases.case6.description": {"message": "持续发展您的批判性和创造性思维能力。"}, "aiflow.testimonials.user1.name": {"message": "<PERSON>"}, "aiflow.testimonials.user1.role": {"message": "市场营销经理"}, "aiflow.testimonials.user1.text": {"message": "作为市场营销经理，创造力是我工作的核心。自从我开始使用FunBlocks AIFlow以来，我的生产力就像火箭般上升了。产品策略会议之前，我会用它来组织市场分析，并且AI还会提供及时的见解，确保我的提案总是命中目标。最近，当我们推出新产品时，我用它进行头脑风暴——想法源源不断，这比盯着PowerPoint幻灯片几个小时要好得多。在跨部门沟通中，一个思维导图就能说出很多话，使讨论更加集中。我的老板甚至问我是否秘密地参加了额外的课程！嗯，我想你可以说我找到了最终的工作伙伴。"}, "aiflow.testimonials.user2.name": {"message": "<PERSON>"}, "aiflow.testimonials.user2.role": {"message": "产品经理"}, "aiflow.testimonials.user2.text": {"message": "我很高兴向每个人推荐FunBlocks AI——它对于提高生产力和创造力非常出色。作为产品经理，我每天都需要处理大量信息和想法。我特别喜欢它的无限白板功能，这允许我自由扩展我的想法。在产品规划之前，我经常用它来组织用户需求和市场趋势。AI提供的及时见解，帮助我集中注意力，并从意想不到的角度提出新想法！"}, "aiflow.testimonials.user3.name": {"message": "<PERSON>"}, "aiflow.testimonials.user3.role": {"message": "自由作家"}, "aiflow.testimonials.user3.text": {"message": "作为自由作家，我经常遭遇写作瓶颈。但是有了这个神奇的工具，我的创造力源泉从不枯竭。我特别喜欢它的AI头脑风暴功能，总是给我带来意外的灵感。加上一键文章生成功能，我的写作效率也翻了一番。现在，我不仅接手了更多的项目，我的作品质量也提高了。对于任何在创意领域工作的人，我强烈推荐FunBlocks AIFlow！"}, "aiflow.testimonials.user4.name": {"message": "<PERSON>"}, "aiflow.testimonials.user4.role": {"message": "大学生"}, "aiflow.testimonials.user4.text": {"message": "当我刚开始大学时，我经常感到跟不上，教授们快速讲解了很多材料。然后我的室友推荐了FunBlocks AIFlow。我绝对爱上了无限白板功能——我可以将我所学的任何内容添加到上面，并且AI会不断扩展它，使我的学习比以前更加深入。并且那还不是全部！使用AIFlow进行头脑风暴和主题学习，允许我从不同学科之间建立跨学科连接。AI甚至提供了现实世界应用的例子，突然使我所学的一切都变得有意义了。这简直是最好的！ 👍👍👍"}, "aiflow.testimonials.user5.name": {"message": "Sophia"}, "aiflow.testimonials.user5.role": {"message": "研究员"}, "aiflow.testimonials.user5.text": {"message": "作为研究员，我经常需要探索复杂的主题并生成新想法。FunBlocks AIFlow对我来说是一个游戏改变者。AI驱动的头脑风暴功能帮助我快速生成全面的思维导图，并且多样化的内容支持允许我在研究中包括各种类型的信息。AI助手提供了有价值的见解和建议，使我的研究过程更加高效和富有成果。"}, "aiflow.testimonials.user6.name": {"message": "<PERSON>"}, "aiflow.testimonials.user6.role": {"message": "教育工作者"}, "aiflow.testimonials.user6.text": {"message": "作为教育工作者，我总是寻找新的方式来吸引我的学生，并提高他们的学习体验。FunBlocks AIFlow对此是一个非常棒的工具。AI驱动的头脑风暴功能帮助我创建互动的和视觉上吸引人的学习材料。即时工作输出功能允许我快速生成演示幻灯片和信息图表，使我的教学更加动态和吸引人。我的学生爱它，我也看到他们对材料的理解和保留能力有了显著的提高。"}, "aiflow.cta.title": {"message": "准备好踏上知识冒险之旅了吗？"}, "aiflow.cta.subtitle": {"message": "加入FunBlocks AIFlow，释放无限认知潜能！"}, "aiflow.cta.button": {"message": "立即开始免费试用"}, "aiflow.faq.title": {"message": "常见问题"}, "aiflow.faq.q1": {"message": "什么是FunBlocks AIFlow？"}, "aiflow.faq.a1": {"message": "FunBlocks AIFlow是一个AI驱动的视觉思维平台，旨在帮助用户进行头脑风暴和生成创新想法。它能够将复杂的概念转化为思维导图、幻灯片和信息图表，结合AI的强大功能与视觉思维技术，显著提升您的认知能力和生产力。"}, "aiflow.faq.q2": {"message": "AIFlow与ChatGPT有何不同？"}, "aiflow.faq.a2": {"message": "ChatGPT提供线性文本对话，而AIFlow提供视觉化、多维度的工作空间。您可以同时在多个方向探索想法，通过视觉层次保持上下文，并即时将您的想法转化为专业交付物。"}, "aiflow.faq.q3": {"message": "我可以用AIFlow创建什么？"}, "aiflow.faq.a3": {"message": "使用AIFlow，您可以创建思维导图、演示幻灯片、信息图表、战略规划、知识地图和复杂主题的视觉分析。平台支持各种思维框架，并可以将您的视觉作品转换为不同格式。"}, "aiflow.faq.q4": {"message": "AIFlow支持哪些AI模型？"}, "aiflow.faq.a4": {"message": "AIFlow支持所有主流AI模型，包括GPT-4、GPT-3.5、<PERSON> 3、<PERSON> 2、Gemini Pro等。您还可以使用自己的API密钥通过AIFlow访问这些模型。"}, "aiflow.faq.q5": {"message": "我可以使用AIFlow与他人协作吗？"}, "aiflow.faq.a5": {"message": "是的，AIFlow支持协作。您可以与团队成员或客户共享思维导图和项目，允许实时反馈和协作idea开发。"}, "aiflow.faq.q6": {"message": "使用AIFlow需要技术技能吗？"}, "aiflow.faq.a6": {"message": "不需要技术技能。AIFlow具有直观的界面，使您能够轻松开始创建思维导图，并使用AI助手进行头脑风暴和探索。"}, "aiflow.faq.q7": {"message": "我可以导出思维导图和项目吗？"}, "aiflow.faq.a7": {"message": "是的，AIFlow允许您以各种格式导出作品，包括PNG、PDF等。您还可以将思维导图直接转换为演示文稿、文档或其他交付物。"}, "aiflow.faq.q8": {"message": "AIFlow只是另一个AI思维导图工具吗？"}, "aiflow.faq.a8": {"message": "AIFlow超越了传统思维导图。它提供无限画布、多样化节点类型、主动AI灵感、一键内容生成和可定制AI提示，使其成为全面的创造力和生产力工具。"}, "aiflow.faq.q10": {"message": "使用AIFlow进行头脑风暴有什么优势？"}, "aiflow.faq.a10": {"message": "AIFlow利用AI技术简化头脑风暴过程，能够快速生成全面的思维导图。其灵活界面允许从不同角度进行探索，并结合经典思维模型帮助产生新颖想法。"}, "aiflow.faq.q11": {"message": "有免费试用吗？"}, "aiflow.faq.a11": {"message": "是的，FunBlocks AIFlow提供免费试用。新用户注册后将获得30次免费AI服务访问，并每天额外获得10次免费访问。"}, "aiflow.faq.q12": {"message": "如何开始使用AIFlow？"}, "aiflow.faq.a12": {"message": "只需点击\"免费试用\"按钮，创建账户，即可立即开始使用AIFlow。试用不需要信用卡，您将可以访问所有功能。"}, "modal.alt": {"message": "放大视图"}, "modal.click_to_close": {"message": "点击关闭"}, "slides.masthead.title": {"message": "智能创作，卓越呈现"}, "slides.masthead.subtitle": {"message": "利用AI，让幻灯片制作和演讲变得轻松高效，为追求卓越内容和高效的人士量身打造"}, "slides.masthead.target": {"message": "为追求卓越内容和高效工作的专业人士量身打造"}, "slides.masthead.cta": {"message": "一键生成幻灯片，免费试用"}, "slides.intro.title": {"message": "FunBlocks AI 幻灯片"}, "slides.intro.description": {"message": "用AI点燃灵感，轻松创建幻灯片"}, "slides.intro.point1.name": {"message": "Markdown书写"}, "slides.intro.point1.description": {"message": "用直观的Markdown语法,瞬间构建专业幻灯片"}, "slides.intro.point2.name": {"message": "极简美学"}, "slides.intro.point2.description": {"message": "专注内容创作,告别繁琐排版"}, "slides.intro.point3.name": {"message": "AI 助手加持"}, "slides.intro.point3.description": {"message": "智能生成与优化内容，激发创意灵感，并提供演讲技巧指导"}, "slides.intro.point4.name": {"message": "生态系统整合"}, "slides.intro.point4.description": {"message": "与FunBlocks文档、AIFlow无缝衔接，一键转化各类内容为精美幻灯片"}, "slides.intro.point5.name": {"message": "云端协作"}, "slides.intro.point5.description": {"message": "随时随地，在线创作、演示、分享"}, "slides.features.title": {"message": "核心功能"}, "slides.features.item1.name": {"message": "Markdown写作"}, "slides.features.item1.description": {"message": "输入文本即可高效制作幻灯片，体验全键盘操作的高效与专注。"}, "slides.features.item2.name": {"message": "斜杠编辑菜单"}, "slides.features.item2.description": {"message": "编辑时，只需输入'/'即可调出编辑菜单，操作便捷，体验如Notion般顺畅。"}, "slides.features.item3.name": {"message": "AI驱动内容生成"}, "slides.features.item3.description": {"message": "根据主题智能生成优质幻灯片内容，或优化现有内容，提升演示效果。"}, "slides.features.item4.name": {"message": "演讲者笔记"}, "slides.features.item4.description": {"message": "AI生成演讲稿，帮助演讲者更好地传达信息。"}, "slides.features.item5.name": {"message": "学术与专业支持"}, "slides.features.item5.description": {"message": "支持KaTeX排版（LaTeX的子集）与代码块高亮，满足学术与专业需求。"}, "slides.features.item6.name": {"message": "在线分享与演示"}, "slides.features.item6.description": {"message": "一键分享，在线演示，告别版本不一致的烦恼。"}, "slides.features.item7.name": {"message": "多主题选择"}, "slides.features.item7.description": {"message": "提供多款简约专业的主题，适应不同场景需求，让内容始终保持焦点。"}, "slides.features.item8.name": {"message": "演讲者视图"}, "slides.features.item8.description": {"message": "包含计时器、下一张幻灯片预览和演讲稿。按S键试用。"}, "slides.features.more": {"message": "点击查看更多功能与演示"}, "slides.platform-synergy.title": {"message": "平台协同：FunBlocks生态系统中的 AI Slides"}, "slides.platform-synergy.description": {"message": "体验AI幻灯片作为FunBlocks AI工作区不可或缺的一部分，创造无缝的知识工作流程"}, "slides.platform-synergy.point1.name": {"message": "集成资源利用"}, "slides.platform-synergy.point1.description": {"message": "即时利用其他FunBlocks工具的内容和想法，生成全面的、准确的幻灯片，无需编写详细的提示"}, "slides.platform-synergy.point2.name": {"message": "增强的AI生成质量"}, "slides.platform-synergy.point2.description": {"message": "从平台范围内的上下文感知中受益，基于您现有的工作区材料，生成异常高质量的幻灯片内容"}, "slides.platform-synergy.point3.name": {"message": "无缝知识呈现"}, "slides.platform-synergy.point3.description": {"message": "轻松地将您的研究、写作和头脑风暴转化为优雅、专业的演示，清晰地传达复杂的想法"}, "slides.platform-synergy.point4.name": {"message": "完整的工作流程集成"}, "slides.platform-synergy.point4.description": {"message": "通过从构思到交流的演示，完成您的生产力循环，创造一个连贯的AI工作区体验"}, "slides.ai-assistant.title": {"message": "AI助手：与你共创精彩演讲的合作伙伴"}, "slides.ai-assistant.description": {"message": "与AI协作，生成幻灯片，优化内容，提升演讲表现，助你打造深入人心的演讲"}, "slides.ai-assistant.point1.name": {"message": "一键生成完整Slides"}, "slides.ai-assistant.point1.description": {"message": "只需输入主题，AI助手即可生成结构完整、内容丰富的整套幻灯片"}, "slides.ai-assistant.point2.name": {"message": "内容优化与润色"}, "slides.ai-assistant.point2.description": {"message": "AI文本优化，使表达更加清晰简洁，突出核心信息"}, "slides.ai-assistant.point3.name": {"message": "创意建议"}, "slides.ai-assistant.point3.description": {"message": "为您的演示提供新颖的观点和角度，增强说服力"}, "slides.ai-assistant.point4.name": {"message": "智能演讲稿生成"}, "slides.ai-assistant.point4.description": {"message": "根据每页主题，生成专业、有说服力的演讲内容，并提供演讲指导"}, "slides.use-cases.title": {"message": "应用场景"}, "slides.use-cases.description": {"message": "AI Slides在各个领域都有广泛且创新的应用"}, "slides.use-cases.point1.name": {"message": "学术汇报"}, "slides.use-cases.point1.description": {"message": "简洁明了地呈现研究成果和学术观点，让听众更容易理解复杂内容"}, "slides.use-cases.point2.name": {"message": "商业演示"}, "slides.use-cases.point2.description": {"message": "快速制作专业的商业报告和策划案，提高工作效率，并提供演讲指导"}, "slides.use-cases.point3.name": {"message": "课程教学"}, "slides.use-cases.point3.description": {"message": "轻松创建引人入胜的教学幻灯片，提升学生参与度"}, "slides.use-cases.point4.name": {"message": "个人分享"}, "slides.use-cases.point4.description": {"message": "为个人想法和故事制作富有吸引力的演示和演讲稿，改进演讲技巧"}, "slides.use-cases.point5.name": {"message": "团队分享"}, "slides.use-cases.point5.description": {"message": "高效分享信息，保持团队同步，提升协作效率"}, "slides.use-cases.point6.name": {"message": "快速汇报"}, "slides.use-cases.desc6": {"message": "适合需要快速制作简洁有力演示的场景，无需花费大量时间在复杂设计上"}, "slides.testimonials.user1.name": {"message": "莎拉"}, "slides.testimonials.user1.role": {"message": "公司员工"}, "slides.testimonials.user1.text": {"message": "作为一名普通的公司员工，FunBlocks AI Slides的AI助手完全改变了我准备工作报告的方式。它帮助我生成和优化幻灯片，使我的内容更加清晰和逻辑。最让我惊讶的是AI助手如何扩展我的思维，使我的提案更加创新和实用，这已经帮助我在上级面前脱颖而出。此外，它还提供了演讲指导，这已经显著地提高了我的沟通技巧。现在，我在团队中更自信地表达我的想法，这一进步对我的职业发展产生了巨大的影响。AI Slides不仅仅是一个工具；它更像是我职业成长的助推器。"}, "slides.testimonials.user2.name": {"message": "阿列克斯"}, "slides.testimonials.user2.role": {"message": "中层企业经理"}, "slides.testimonials.user2.text": {"message": "作为一名中层经理，我每周都需要汇报业务和工作进度。AIFlow简化了这个过程，帮助我快速提炼核心内容，生成简洁的演示。它还生成演讲笔记，使我的报告更加有条理。以前，我花了很多时间组织材料，但现在我可以在短时间内完成一份专业的报告。AIFlow极大地提高了我的工作效率，是专业人士不可或缺的工具。"}, "slides.testimonials.user3.name": {"message": "马丁"}, "slides.testimonials.user3.role": {"message": "大学教授"}, "slides.testimonials.user3.text": {"message": "FunBlocks AI Slides是我使用过的最好的教学工具之一。作为一名计算机科学教授，我经常需要创建包含代码和技术概念的复杂幻灯片。这个工具的Markdown支持让我轻松插入代码，而AI助手帮助将复杂的内容转换为学生可以轻松理解的解释。最让人印象深刻的是，它可以根据课程大纲自动生成完整的幻灯片，让我更多地专注于教学本身。FunBlocks AI Slides是教育工作者不可或缺的工具。"}, "slides.testimonials.user4.name": {"message": "迈克"}, "slides.testimonials.user4.role": {"message": "市场策略经理"}, "slides.testimonials.user4.text": {"message": "在市场营销中，速度和创造力同样重要。FunBlocks AI Slides完美地满足了这两个需求。它的AI助手就像我的个人创意顾问，总是提供新灵感当我需要时。在FunBlocks平台上，我通常从AIFlow开始，用于头脑风暴，获取新想法和解决方案，然后快速生成幻灯片。这个工作流程既高效又有创造力。FunBlocks是市场策略家必备的工具。"}, "slides.testimonials.user5.name": {"message": "埃米莉"}, "slides.testimonials.user5.role": {"message": "研究生"}, "slides.testimonials.user5.text": {"message": "作为一名研究生，我经常需要准备学术报告和答辩。FunBlocks AI Slides使这变得更加容易。它支持Markdown，允许轻松插入复杂的数学公式和引用，而AI助手优化了我的表达，使我的演示更加清晰和有说服力。极简设计风格让我专注于内容本身，而不是浪费时间在格式调整上。FunBlocks AI Slides绝对是一个每个学生和学者的强大工具，使你的学术工作效率提高一倍。"}, "slides.testimonials.user6.name": {"message": "约翰"}, "slides.testimonials.user6.role": {"message": "自由设计师"}, "slides.testimonials.user6.text": {"message": "作为一名自由设计师，我经常需要向客户展示我的设计概念。FunBlocks AI Slides对我来说是一个游戏规则改变者。它允许我快速、高效地创建专业的演示，专注于内容而不是设计。AI助手帮助我生成和优化内容，使我的演示更加有影响力。它是一个每个设计师都应该拥有的工具。"}, "slides.cta.title": {"message": "准备好体验全新的 AI 幻灯片了吗？"}, "slides.cta.subtitle": {"message": "加入FunBlocks AI Slides，轻松创建出色幻灯片！"}, "slides.cta.button": {"message": "立即开始免费试用"}, "slides.faq.title": {"message": "常见问题"}, "slides.faq.q1": {"message": "我不熟悉Markdown语法，可以用FunBlocks AI Slides吗？"}, "slides.faq.a1": {"message": "当然可以！FunBlocks AI Slides提供简单的Markdown指南，让您快速上手。编辑时，输入'/'即可调出编辑菜单，类似于Notion的操作体验，非常方便。此外，我们的AI助手会帮您生成和优化内容，您无需担心语法问题。我们希望您专注于内容，而不是工具的使用。"}, "slides.faq.q2": {"message": "FunBlocks AI Slides如何提高我的工作效率？"}, "slides.faq.a2": {"message": "通过简化幻灯片制作流程，FunBlocks AI Slides帮助您节省大量时间。AI内容生成、一键主题应用、全键盘操作等功能都旨在让您更快地完成高质量演示。您可以将更多精力投入到内容本身，而不是花费大量时间在格式调整上。"}, "slides.faq.q3": {"message": "FunBlocks AI Slides适合哪些用户？"}, "slides.faq.a3": {"message": "FunBlocks AI Slides特别适合注重效率、重视内容质量大于形式的专业人士。无论您是学者、教育工作者、企业管理者还是自由职业者，只要您希望快速制作出高质量、专业的演示文稿，FunBlocks AI Slides都是您的理想之选。"}, "slides.faq.q4": {"message": "使用FunBlocks AI Slides制作的幻灯片是否看起来过于简单？"}, "slides.faq.a4": {"message": "不会。虽然我们强调简洁和效率，但FunBlocks AI Slides提供的主题和设计都经过精心挑选，确保既专业又美观。我们的理念是'内容为王'，帮助您通过清晰、有力的演示传达核心信息，而不是依赖华丽但可能分散注意力的设计元素。"}, "slides.faq.q5": {"message": "FunBlocks AI Slides如何保护我的数据和隐私？"}, "slides.faq.a5": {"message": "我们非常重视用户的数据安全和隐私。所有数据都经过加密存储，且我们不会将您的内容用于其他目的。AI功能的使用也遵循严格的隐私保护准则。您可以放心使用FunBlocks AI Slides，专注于创作优秀的演示内容。"}, "slides.faq.q6": {"message": "FunBlocks AI Slides与传统PPT软件相比有什么优势？"}, "slides.faq.a6": {"message": "FunBlocks AI Slides专注于提高效率和内容质量。主要优势包括：1. 基于Markdown的快速创作；2. AI辅助内容生成和优化；3. 极简设计，减少格式调整时间；4. 在线协作和分享；5. 与FunBlocks生态系统的无缝集成。这些特性让您能够更快地创建高质量演示，而不是纠结于复杂的设计细节。"}, "slides.faq.q7": {"message": "我可以导入现有的PPT到FunBlocks AI Slides吗？"}, "slides.faq.a7": {"message": "目前，FunBlocks AI Slides不直接支持导入传统PPT文件。但是，我们的AI助手可以帮助您快速重建现有内容。只需将PPT的主要内容粘贴到我们的编辑器中，AI就能帮助您重组和优化内容，创建出新的、更高效的演示版本。"}, "slides.faq.q8": {"message": "听说亚马逊内部不允许用PPT开会，是真的吗？"}, "slides.faq.a8": {"message": "是的。创始人贝索斯认为PPT太简略，容易忽略关键信息，不利于信息传达和决策，所以在公司内部禁止使用，改用\"6页文档\"。但FunBlocks AI Slides通过增强演讲者备注解决了这个问题，AI助手可以写出详细的演讲内容作为备注，既帮演讲者，也能为听众提供足够的细节信息。"}, "slides.faq.q9": {"message": "使用AI生成的内容会有版权问题吗？"}, "slides.faq.a9": {"message": "FunBlocks AI Slides的AI生成内容是基于您提供的关键词和主题，生成的是原创内容。这些内容的版权归属于您。但我们建议您仍然审核AI生成的内容，确保其准确性和适用性。对于任何引用或特定数据，我们建议您进行适当的引用和核实。"}, "slides.faq.q10": {"message": "FunBlocks AI Slides适合制作复杂的科技或数据演示吗？"}, "slides.faq.a10": {"message": "绝对适合。尽管FunBlocks AI Slides强调简洁，但它完全支持复杂的科技和数据演示。您可以轻松插入代码块、数学公式、图表和图片。我们的AI助手还可以帮助解释复杂概念，使您的演示更容易被观众理解。"}, "slides.faq.q11": {"message": "我能自定义FunBlocks AI Slides的主题和样式吗？"}, "slides.faq.a11": {"message": "是的，FunBlocks AI Slides提供了多种预设主题，同时也支持自定义。您可以调整颜色、字体和布局来匹配您的品牌风格。但请记住，我们的设计理念是保持简洁，让内容成为主角。"}, "slides.faq.q12": {"message": "FunBlocks AI Slides如何处理图片和多媒体内容？"}, "slides.faq.a12": {"message": "虽然FunBlocks AI Slides主要基于文本，但它完全支持图片、视频和其他多媒体内容的插入。您可以通过简单的Markdown语法或Slash菜单添加这些元素。AI助手还可以帮助您选择或生成与内容相关的图片建议。"}, "slides.faq.q13": {"message": "离线时可以使用FunBlocks AI Slides吗？"}, "slides.faq.a13": {"message": "FunBlocks AI Slides主要是一个在线工具，但我们支持下载为PDF版本。"}, "slides.faq.q14": {"message": "FunBlocks AI Slides是否支持演示者模式和计时功能？"}, "slides.faq.a14": {"message": "是的，FunBlocks AI Slides提供专业的演示者模式。您可以查看下一张幻灯片、演讲者注释，并使用内置计时器控制演讲节奏。这些功能都经过优化，确保您在演示时既专业又从容。"}, "slides.faq.q15": {"message": "如何开始使用FunBlocks AI Slides？新手友好吗？"}, "slides.faq.a15": {"message": "FunBlocks AI Slides非常欢迎新手！我们提供了详细的新手指南和交互式教程，帮助您快速上手。从开始使用的那一刻起，我们的AI助手就会全程指导您，解答疑问，提供建议。您会发现，创建第一份专业演示文稿比想象中更简单快捷。"}, "slides.faq.q16": {"message": "我可以免费使用FunBlocks AI吗？"}, "slides.faq.a16": {"message": "是的，FunBlocks AI提供免费使用选项。新用户注册后将获得30次免费AI服务访问，每天还会额外赠送10次免费访问，确保您能够体验到AI助手的强大功能。此外，FunBlocks AI还设有奖励机制，邀请好友注册，您和好友都能获得免费使用额度。"}, "slides.faq.q17": {"message": "如何通过邀请好友获得免费使用额度奖励呢？"}, "slides.faq.a17": {"message": "您可以通过两种方式邀请好友。第一，分享邀请链接，好友通过链接注册后即可获得奖励。第二，分享FunBlocks AI浏览器插件或AI Flow生成的内容，用户点击阅读后注册，您也将获得邀请奖励。"}, "slides.faq.q18": {"message": "如何取消订阅的Plan？"}, "slides.faq.a18": {"message": "您可以在设置中找到\"升级 AI 会员\"栏目。如果您订阅了某个会员计划，在相应的介绍栏中会有Cancel按钮，点击即可退订。"}, "slides.head.title": {"message": "FunBlocks AI 幻灯片：高效AI演示工具 | 从头脑风暴到演示的一站式工作空间"}, "slides.head.description": {"message": "使用FunBlocks AI幻灯片轻松创建精美、内容丰富的演示文稿。我们的AI驱动工具结合了头脑风暴、思维导图和批判性思维功能，帮助您创建有影响力的演示文稿。适合教育工作者、专业人士和学生。"}, "slides.comparison.title": {"message": "FunBlocks AI幻灯片的优势对比"}, "slides.comparison.description": {"message": "FunBlocks AI幻灯片通过在一个无缝工作空间中整合头脑风暴、思维导图和批判性思维工具，提供了独特的演示文稿创建方法"}, "slides.comparison.funblocksHeader": {"message": "FunBlocks AI幻灯片"}, "slides.comparison.powerPointHeader": {"message": "PowerPoint"}, "slides.comparison.googleSlidesHeader": {"message": "Google幻灯片"}, "slides.comparison.canvaHeader": {"message": "<PERSON><PERSON>"}, "slides.comparison.feature1": {"message": "AI驱动的内容生成"}, "slides.comparison.feature2": {"message": "集成头脑风暴工具"}, "slides.comparison.feature3": {"message": "思维导图集成"}, "slides.comparison.feature4": {"message": "批判性思维增强"}, "slides.comparison.feature5": {"message": "基于Markdown的创建"}, "slides.comparison.feature6": {"message": "演讲稿生成"}, "slides.comparison.feature7": {"message": "无干扰界面"}, "slides.comparison.feature8": {"message": "思维导图一键转换"}, "slides.comparison.note": {"message": "FunBlocks AI幻灯片专注于增强您从头脑风暴到演示的思维过程，帮助您创建更有影响力、结构更完善的内容，而不仅仅关注设计元素。"}, "slides.thinking_process.title": {"message": "从头脑风暴到演示：完整的思维过程"}, "slides.thinking_process.description": {"message": "FunBlocks AI提供从初始想法到精美演示的无缝旅程，同时增强批判性和创造性思维"}, "slides.thinking_process.step1.title": {"message": "AI辅助头脑风暴"}, "slides.thinking_process.step1.description": {"message": "使用AI驱动的头脑风暴工具生成想法和探索概念，帮助拓展思维并发现新视角"}, "slides.thinking_process.step2.title": {"message": "思维导图组织"}, "slides.thinking_process.step2.description": {"message": "使用我们直观的思维导图工具直观地构建您的思想，在想法之间创建逻辑连接"}, "slides.thinking_process.step3.title": {"message": "应用批判性思维"}, "slides.thinking_process.step3.description": {"message": "使用批判性思维工具分析和完善您的想法，帮助识别优势、劣势和改进机会"}, "slides.thinking_process.step4.title": {"message": "创建有影响力的幻灯片"}, "slides.thinking_process.step4.description": {"message": "一键将您组织好的思想转化为引人入胜的幻灯片，保持思维过程的逻辑结构"}, "extension_welcome.head.title": {"message": "FunBlocks AI浏览器扩展 - AI驱动的头脑风暴、思维导图和批判性思维工具 | 提升生产力"}, "extension_welcome.head.description": {"message": "使用FunBlocks AI扩展改变您的浏览体验。提供AI驱动的头脑风暴、思维导图、批判性思维框架（六顶思考帽、SWOT分析）和创意写作工具。生成信息图表、洞察卡片和可视化思维导图。兼容ChatGPT、Claude、Gemini Pro。增强您在线阅读、写作和思考的方式。"}, "extension_welcome.benefits.title": {"message": "主要优势"}, "extension_welcome.benefits.benefit1.title": {"message": "提高生产力"}, "extension_welcome.benefits.benefit1.description": {"message": "通过AI驱动的工具节省时间，提高工作效率，简化阅读、写作和思考过程。"}, "extension_welcome.benefits.benefit2.title": {"message": "增强批判性思维"}, "extension_welcome.benefits.benefit2.description": {"message": "通过结构化思维框架（如六顶思考帽、SWOT分析和第一性原理思维）培养更强的分析能力。"}, "extension_welcome.benefits.benefit3.title": {"message": "激发创造力"}, "extension_welcome.benefits.benefit3.description": {"message": "通过AI驱动的头脑风暴和思维导图工具拓展创意视野，发现新的想法和视角。"}, "extension_welcome.benefits.benefit4.title": {"message": "无缝集成"}, "extension_welcome.benefits.benefit4.description": {"message": "适用于所有网站，上下文感知工具能理解您的操作并提供相关帮助。"}, "extension_welcome.benefits.benefit5.title": {"message": "可视化学习"}, "extension_welcome.benefits.benefit5.description": {"message": "将复杂信息转化为思维导图、信息图表和洞察卡片等可视化格式，提高理解和记忆。"}, "extension_welcome.benefits.benefit6.title": {"message": "隐私与控制"}, "extension_welcome.benefits.benefit6.description": {"message": "选择您偏好的AI模型，通过灵活的隐私选项保持对数据的控制。"}, "extension_welcome.nav.home": {"message": "FunBlocks - AI 助手"}, "extension_welcome.nav.settings": {"message": "个性化设置"}, "extension_welcome.nav.reading": {"message": "AI阅读"}, "extension_welcome.nav.writing": {"message": "AI写作"}, "extension_welcome.nav.contextual": {"message": "快捷AI工具"}, "extension_welcome.nav.pricing": {"message": "会员方案"}, "extension_welcome.nav.login": {"message": "登录"}, "extension_welcome.pin.title": {"message": "开启FunBlocks AI之旅"}, "extension_welcome.pin.desc": {"message": "让AI助手成为您网上冲浪的得力助手"}, "extension_welcome.pin.li1": {"message": "点击浏览器右上角的扩展图标"}, "extension_welcome.pin.li2": {"message": "将FunBlocks AI固定到工具栏"}, "extension_welcome.pin.li3": {"message": "现在，AI助手随时待命，为您提供最强AI服务"}, "extension_welcome.pin.more": {"message": "继续探索FunBlocks AI的强大功能 ⬇️"}, "extension_welcome.settings.title": {"message": "个性化您的AI助手，提高您的生产效率"}, "extension_welcome.settings.subtitle": {"message": "通过广泛的自定义选项，您可以轻松地根据您的需求调整AI助手"}, "extension_welcome.settings.inbox_llms.title": {"message": "单一登录解锁高级LLM模型"}, "extension_welcome.settings.inbox_llms.li1": {"message": "访问像ChatGPT-4o、Claude-3.7、Gemini-2.5-Pro、DeepSeek等顶级AI模型"}, "extension_welcome.settings.inbox_llms.li2": {"message": "所有领先的AI模型在一个地方，顺畅地集成到您的工作流程中"}, "extension_welcome.settings.inbox_llms.li3": {"message": "享受单一支付的成本效益解决方案，无需为每个模型订阅"}, "extension_welcome.settings.private_llms.title": {"message": "或使用您自己的LLM API密钥"}, "extension_welcome.settings.private_llms.li1": {"message": "ChatGPT、<PERSON>、<PERSON>序列号，或者OpenAI兼容的API"}, "extension_welcome.settings.private_llms.li2": {"message": "使用您自己的API密钥，免费享受所有AI功能"}, "extension_welcome.reading.title": {"message": "智能阅读：AI边栏助手"}, "extension_welcome.reading.subtitle": {"message": "便捷的浏览器侧边栏工具"}, "extension_welcome.reading.desc": {"message": "向右看👉，将鼠标悬停在FunBlocks AI图标上，即可使用："}, "extension_welcome.reading.li1": {"message": "页面助手：快速理解并分析当前页面内容"}, "extension_welcome.reading.li2": {"message": "全局助手：无限制的AI交互与任务处理"}, "extension_welcome.reading.li3": {"message": "AI Flow：以思维导图方式深入探索主题"}, "extension_welcome.reading.li4": {"message": "智能截图：AI分析屏幕任意区域内容"}, "extension_welcome.reading.critical_thinking": {"message": "FunBlocks AI不仅提高阅读速度，还培养批判性思维："}, "extension_welcome.reading.ct1": {"message": "提取关键信息，总结要点"}, "extension_welcome.reading.ct2": {"message": "拓展相关知识，加深理解"}, "extension_welcome.reading.ct3": {"message": "识别潜在偏见，辨别信息真伪"}, "extension_welcome.contextual.title": {"message": "上下文AI工具栏"}, "extension_welcome.contextual.subtitle": {"message": "FunBlocks AI扩展使用页面或选定的内容作为上下文来执行AI任务，无需在应用程序之间进行复制粘贴"}, "extension_welcome.contextual.toolbar": {"message": "选择任何文本，立即弹出AI工具栏"}, "extension_welcome.contextual.toolbar_desc": {"message": "一键翻译、解释、润色，并继续写作 - AI助手随时待命"}, "extension_welcome.contextual.try_now": {"message": "现在试试，选择下面的文本："}, "extension_welcome.contextual.select_text": {"message": "FunBlocks AI：您的全方位AI阅读和写作助手"}, "extension_welcome.contextual.widget": {"message": "AI小部件"}, "extension_welcome.contextual.widget_desc": {"message": "在Twitter、Reddit、Gmail和YouTube等平台上，一键生成回复并总结视频内容"}, "extension_welcome.writing.title": {"message": "AI写作助手"}, "extension_welcome.writing.subtitle": {"message": "在左下方编辑框体验AI辅助写作"}, "extension_welcome.writing.methods": {"message": "多种方式激活AI写作："}, "extension_welcome.writing.li1": {"message": "选中文本，唤起工具栏"}, "extension_welcome.writing.li2": {"message": "点击右下角蓝色图标"}, "extension_welcome.writing.li3": {"message": "输入'/'快捷指令"}, "extension_welcome.writing.features": {"message": "核心功能"}, "extension_welcome.writing.f1": {"message": "一键生成主题文章"}, "extension_welcome.writing.f2": {"message": "智能润色与语法纠错"}, "extension_welcome.writing.f3": {"message": "灵活续写与内容扩展"}, "extension_welcome.writing.f4": {"message": "上下文智能回复（邮件、社交媒体等）"}, "extension_welcome.useCases.title": {"message": "使用场景"}, "extension_welcome.useCases.description": {"message": "FunBlocks AI适应各种场景，在不同环境中提升您的生产力和创造力。"}, "extension_welcome.useCases.case1.title": {"message": "学生专用"}, "extension_welcome.useCases.case1.description": {"message": "通过AI驱动的笔记、学习材料批判性分析和作业项目头脑风暴，提升学习效果。"}, "extension_welcome.useCases.case1.benefit1": {"message": "将复杂阅读材料转化为可视化思维导图"}, "extension_welcome.useCases.case1.benefit2": {"message": "使用批判性思维框架生成结构化学习笔记"}, "extension_welcome.useCases.case1.benefit3": {"message": "进行论文主题头脑风暴并构建论点"}, "extension_welcome.useCases.case2.title": {"message": "职场人士专用"}, "extension_welcome.useCases.case2.description": {"message": "通过AI辅助邮件、报告和研究，提高生产力。高效生成专业内容和分析信息。"}, "extension_welcome.useCases.case2.benefit1": {"message": "几秒钟内起草专业邮件和回复"}, "extension_welcome.useCases.case2.benefit2": {"message": "将会议记录转化为可执行的摘要"}, "extension_welcome.useCases.case2.benefit3": {"message": "从任何网页内容创建演示幻灯片"}, "extension_welcome.useCases.case3.title": {"message": "作家和内容创作者专用"}, "extension_welcome.useCases.case3.description": {"message": "通过AI驱动的头脑风暴、研究辅助和内容润色工具，提升创造力和生产力。"}, "extension_welcome.useCases.case3.benefit1": {"message": "使用AI头脑风暴生成创意内容想法"}, "extension_welcome.useCases.case3.benefit2": {"message": "使用批判性思维框架深入研究主题"}, "extension_welcome.useCases.case3.benefit3": {"message": "创建可视化信息图表和洞察卡片"}, "extension_welcome.useCases.case4.title": {"message": "研究人员专用"}, "extension_welcome.useCases.case4.description": {"message": "通过AI驱动的分析、结构化思维框架和复杂信息的可视化组织，加速研究。"}, "extension_welcome.useCases.case4.benefit1": {"message": "使用批判性思维工具分析研究论文"}, "extension_welcome.useCases.case4.benefit2": {"message": "使用思维导图组织复杂信息"}, "extension_welcome.useCases.case4.benefit3": {"message": "生成研究摘要和可视化摘要"}, "extension_welcome.comparison.title": {"message": "FunBlocks AI对比"}, "extension_welcome.comparison.description": {"message": "了解FunBlocks AI扩展如何凭借其独特的头脑风暴、思维导图和批判性思维功能组合脱颖而出。"}, "extension_welcome.comparison.featureHeader": {"message": "功能"}, "extension_welcome.comparison.funblocksHeader": {"message": "FunBlocks AI"}, "extension_welcome.comparison.chatgptHeader": {"message": "<PERSON>"}, "extension_welcome.comparison.grammarlyHeader": {"message": "Grammarly"}, "extension_welcome.comparison.otherHeader": {"message": "其他AI扩展"}, "extension_welcome.comparison.feature1": {"message": "AI驱动的头脑风暴"}, "extension_welcome.comparison.feature2": {"message": "可视化思维导图"}, "extension_welcome.comparison.feature3": {"message": "批判性思维框架"}, "extension_welcome.comparison.feature4": {"message": "上下文AI工具栏"}, "extension_welcome.comparison.feature5": {"message": "信息图表和洞察卡片创建"}, "extension_welcome.comparison.feature6": {"message": "多模型AI支持"}, "extension_welcome.comparison.feature7": {"message": "上下文感知小部件"}, "extension_welcome.comparison.feature8": {"message": "提供免费版本"}, "extension_welcome.comparison.note": {"message": "FunBlocks浏览器扩展通过AI驱动的写作、阅读和批判性思维工具增强您的日常在线阅读和写作体验。它提供了头脑风暴、思维导图和批判性思维功能的独特组合，帮助您更有效地在线阅读、写作和思考。与其他扩展不同，FunBlocks提供了全面的认知增强工具套件，超越了基本的AI辅助。"}, "extension_welcome.video.title": {"message": "观看FunBlocks AI实际操作"}, "extension_welcome.video.description": {"message": "了解FunBlocks AI扩展如何通过AI驱动的阅读助手、写作助手和批判性思维工具增强您的浏览体验。"}, "extension_welcome.video.feature1.title": {"message": "AI写作助手"}, "extension_welcome.video.feature1.description": {"message": "通过AI驱动的内容创作、编辑和头脑风暴工具提升您的写作能力"}, "extension_welcome.video.feature2.title": {"message": "AI阅读助手"}, "extension_welcome.video.feature2.description": {"message": "通过AI驱动的摘要、分析和可视化功能，改变您的在线阅读方式"}, "extension_welcome.video.feature3.title": {"message": "认知增强工具"}, "extension_welcome.video.feature3.description": {"message": "通过结构化框架、思维导图和批判性分析工具提升您的思考能力"}, "extension_welcome.cta.title": {"message": "还没有安装 FunBlocks AI 插件吗？"}, "extension_welcome.cta.subtitle": {"message": "您正在错过一个由 AI 驱动的高效和创造力的世界！"}, "extension_welcome.cta.button": {"message": "立即开始您的免费试用"}, "extension_welcome.testimonials.user1.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user1.role": {"message": "内容策略师"}, "extension_welcome.testimonials.user1.text": {"message": "FunBlocks AI 扩展彻底改变了我的在线工作流程。AI写作助手帮助我在任何网站上直接起草和润色内容，而批判性思维工具则帮助我更深入地分析信息。我特别喜欢上下文工具栏如何在我需要时精确出现，根据我正在做的事情提供相关的AI辅助。只需一键即可从复杂文章创建可视化思维导图的功能，对内容规划来说是革命性的。这个扩展不仅仅是一个工具——它就像是一个跟随我浏览网页的完整AI工作空间。"}, "extension_welcome.testimonials.user2.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user2.role": {"message": "研究分析师"}, "extension_welcome.testimonials.user2.text": {"message": "作为研究分析师，我需要快速处理大量信息。FunBlocks AI扩展的页面助手功能让我能够立即从行业报告和研究论文中提取关键见解。批判性思维框架帮助我识别我正在审查的内容中潜在的偏见和逻辑谬误。这个扩展的与众不同之处在于与AI Flow的无缝集成，用于更深入的分析——我可以将复杂数据转化为视觉思维导图，揭示我可能错过的联系。能够根据不同任务在不同AI模型（ChatGPT、Claude、Gemini、Groq）之间进行选择，对专业研究需求来说非常有价值。"}, "extension_welcome.testimonials.user3.name": {"message": "Sophia"}, "extension_welcome.testimonials.user3.role": {"message": "研究生"}, "extension_welcome.testimonials.user3.text": {"message": "FunBlocks AI扩展完全改变了我的学习方式。AI阅读助手通过提供复杂学术论文的摘要和难懂概念的解释，帮助我理解它们。我使用头脑风暴工具从多个角度探索研究主题，这显著提高了我的论文质量。思维导图功能帮助我直观地组织思想，建立不同理论和概念之间的联系。我最欣赏的是它如何增强我的批判性思维能力——它不仅仅给我答案，还帮助我发展更深层次的分析能力，这一点已经得到了我的教授们的注意和赞扬。"}, "extension_welcome.testimonials.user4.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user4.role": {"message": "数字营销专家"}, "extension_welcome.testimonials.user4.text": {"message": "FunBlocks AI扩展是我在多个平台上创建吸引人内容的秘密武器。AI小部件使生成电子邮件回复、社交媒体帖子和广告文案变得非常高效。我对信息图表和洞察卡片功能特别印象深刻——我可以在任何网页上选择文本，立即创建能够吸引用户参与的视觉内容。扩展理解上下文的能力意味着AI建议总是与我正在处理的内容相关。自从安装这个扩展以来，我的内容创建时间减半，同时实际上提高了质量。创造性思维工具和实用AI辅助的结合是无与伦比的。"}, "extension_welcome.testimonials.user5.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user5.role": {"message": "语言教师"}, "extension_welcome.testimonials.user5.text": {"message": "我经常使用FunBlocks AI扩展来增强我的教学材料，帮助学生理解复杂文本。翻译和解释工具对语言学习来说是无价的，让学生能够在上下文中理解困难概念。这个扩展的特别之处在于它促进批判性思维，而不仅仅是提供答案。我可以选择段落，让AI创建引人深思的问题或生成视觉概念图，帮助学生看到想法之间的关系。将生成的内容保存到备忘录以供日后查看的功能，为我和我的学生创建了一个有价值的资源库。"}, "extension_welcome.testimonials.user6.name": {"message": "<PERSON>"}, "extension_welcome.testimonials.user6.role": {"message": "创业公司创始人"}, "extension_welcome.testimonials.user6.text": {"message": "经营创业公司意味着要扮演多种角色，而FunBlocks AI扩展帮助我在所有角色中都表现出色。我用它来做各种事情，从起草投资者邮件到分析竞争对手网站。头脑风暴和思维导图工具对产品开发至关重要，帮助我们从多个角度探索想法。最让我印象深刻的是灵活性——我可以根据任务使用自己的API密钥连接不同的AI模型，这在我的浏览器中提供了企业级AI能力。扩展的上下文感知功能意味着我花更少的时间解释我需要什么，花更多的时间实施好的想法。这就像在我的指尖上拥有一个完整的AI部门。"}, "extension_welcome.faq.q1": {"message": "使用自己申请的大模型API Key，需要支付FunBlocks AI服务费吗？"}, "extension_welcome.faq.a1": {"message": "不需要，用户使用自己的API Key，只需支付服务提供方的费用，无需支付FunBlocks AI服务费，也不受每日免费访问额度限制。"}, "extension_welcome.faq.q2": {"message": "没有大模型API Key，是否可以使用AI助手？"}, "extension_welcome.faq.a2": {"message": "可以，注册登录FunBlocks服务后，即可获得40次免费AI服务访问额度，每天还有10次免费访问额度。您也可以加入FunBlocks AI会员服务，享受不受次数限制的AI服务。"}, "extension_welcome.faq.q3": {"message": "FunBlocks AI有这么多产品，是不是很复杂，需要很高的学习门槛？"}, "extension_welcome.faq.a3": {"message": "FunBlocks AI有多个产品，分别满足不同的知识工作应用场景。这些产品可以协作也可以独立使用，按需选择即可，无需完整学习。FunBlocks AI设计简单易用，建议从浏览器插件开始体验，一键安装即可。"}, "extension_welcome.faq.q4": {"message": "为什么特别强调浏览器插件？"}, "extension_welcome.faq.a4": {"message": "浏览器插件将AI助手引入所有网页应用中，不局限于FunBlocks平台。用户可以随时使用，无需复制粘贴。插件还支持情景化功能，如一键回复邮件、评论、总结页面内容等，大大提高工作效率。"}, "extension_welcome.faq.q5": {"message": "FunBlocks AI为什么要做这么多产品？"}, "extension_welcome.faq.a5": {"message": "FunBlocks AI针对知识工作者的不同场景设计产品，提升工作效率。利用大语言模型（LLM）的能力解决用户问题，简化操作。例如，一键AI回复邮件、生成针对性内容等，比ChatGPT和Claude更高效。"}, "extension_welcome.faq.q8": {"message": "有了ChatGPT，我为什么还要使用FunBlocks AI？"}, "extension_welcome.faq.a8": {"message": "相比直接使用ChatGPT，FunBlocks AI有很多优势。FunBlocks AI的各个产品都深入用户的工作流中，直接获取任务上下文，组合相应优化的Prompt，LLM返回的结果也直接应用到工作流中，提供了极大的便利性。同时，用户无需自己学习优化和LLM对话的Prompt。另外，像FunBlocks AI Flow这个产品提供的创新的多视角、多支线、视图化交互方式，激发用户创意和思考，是ChatGPT所难以提供的"}, "extension_welcome.faq.q10": {"message": "FunBlocks和Notion有哪些差异？"}, "extension_welcome.faq.a10": {"message": "FunBlocks更注重AI，支持所有网站应用的浏览器插件，用户自定义AI Prompt应用和应用商店。支持所有主流第三方大模型API接口(ChatGPT, Claude, Gemini, Groq)，提供大模型选择权，帮助用户以10倍速工作和学习。"}, "extension_welcome.faq.q11": {"message": "在FunBlocks AI浏览器插件里将AI生成结果保存到Memo中，如何查看和回顾？"}, "extension_welcome.faq.a11": {"message": "登录FunBlocks AI工作空间 https://app.funblocks.net 后，如果之前执行了\"保存结果到即时笔记\"操作，FunBlocks空间中将有Memo页面，按条显示之前保存的生成结果及相关信息。"}, "extension_welcome.faq.q12": {"message": "为什么FunBlocks AI浏览器插件支持所有主流大语言模型？"}, "extension_welcome.faq.a12": {"message": "FunBlocks AI 浏览器插件致力于为用户提供灵活的选择和最佳体验。通过支持所有主流大语言模型，我们满足不同用户的需求，确保您可以根据个人偏好和工作流程选择最合适的工具，从而提升工作效率和创造力。"}, "extension_welcome.faq.q13": {"message": "FunBlocks AI浏览器插件适合学生使用吗？"}, "extension_welcome.faq.a13": {"message": "是的，FunBlocks AI浏览器插件非常适合学生使用。它提供多种功能，帮助学生提高学习效率。例如，您可以使用FunBlocks AI助手在网页上选择词语或段落进行详细解释，或进行批判性阅读和思考，提升思维能力。此外，FunBlocks AI浏览器插件还支持一键调用AIFlow，结合思维导图和AI助手的引导，深入探索问题或主题。写作助手功能可以对学生的作文进行批改和反馈，帮助提升写作技巧和能力。通过这些功能，FunBlocks AI助力学生在学习过程中更加高效和全面。"}, "extension_welcome.faq.q14": {"message": "FunBlocks AI浏览器插件适合职场办公使用吗？"}, "extension_welcome.faq.a14": {"message": "是的，FunBlocks AI非常适合职场使用。它提供多种功能，帮助职场人士提高工作效率。例如，使用FunBlocks AI浏览器插件，您可以在任意网页中进行文本编辑，AI写作助手可以帮助撰写草稿、润色文本、改正错误和翻译，大幅提升写作效率。同时，FunBlocks AI Flow可以帮助您进行项目方案探索和任务分解，让复杂工作变得简单。"}, "extension_welcome.faq.q15": {"message": "FunBlocks AI浏览器插件适合终身学习者使用吗？"}, "extension_welcome.faq.a15": {"message": "是的，FunBlocks AI浏览器插件非常适合**终身学习者**。在信息爆炸的时代，几乎每个人都需要提升思维能力和学习能力。FunBlocks AI浏览器插件通过多种功能满足这一需求，提供全方位的批判性阅读和思考工具，帮助用户辨别信息，获得更全面的认知视角，提升思维能力。特别是 FunBlocks AI Flow，结合思维导图、白板和AI的解决方案，使用户的思考更加流畅和富有创意，充分发挥和提升思考能力。"}, "extension_welcome.faq.q16": {"message": "我可以免费使用FunBlocks AI吗？"}, "extension_welcome.faq.a16": {"message": "是的，FunBlocks AI提供免费使用选项。新用户注册后将获得30次免费AI服务访问，每天还会额外赠送10次免费访问，确保您能够体验到AI助手的强大功能。此外，FunBlocks AI还设有奖励机制，邀请好友注册，您和好友都能获得免费使用额度。"}, "extension_welcome.faq.q17": {"message": "如何通过邀请好友获得免费使用额度奖励呢？"}, "extension_welcome.faq.a17": {"message": "您可以通过两种方式邀请好友。第一，分享邀请链接，好友通过链接注册后即可获得奖励。第二，分享FunBlocks AI浏览器插件或AI Flow生成的内容，用户点击阅读后注册，您也将获得邀请奖励。"}, "extension_welcome.faq.q18": {"message": "如何个性化我的FunBlocks AI助手？"}, "extension_welcome.faq.a18": {"message": "FunBlocks AI提供多种个性化选项，您可以选择主流的大语言模型，构建符合您需求的AI提示应用，并从应用市场中挑选所需的应用程序，以增强AI助手的功能。"}, "aidocs.masthead.title": {"message": "写作更智能，思考更深入"}, "aidocs.masthead.subtitle": {"message": "通过AI动力的文档编辑器和批判性思维辅助提升您的写作能力"}, "aidocs.masthead.cta": {"message": "免费试用"}, "aidocs.intro.title": {"message": "FunBlocks AI Docs：AI动力的文档和页面编辑器"}, "aidocs.intro.description": {"message": "FunBlocks AI Docs结合了类似Notion的编辑功能和AI辅助，以提升您的写作、批判性思维和生产力。"}, "aidocs.intro.point1.name": {"message": "块编辑器"}, "aidocs.intro.point1.description": {"message": "类似Notion的块编辑器，直观灵活地创建文档"}, "aidocs.intro.point2.name": {"message": "AI写作助手"}, "aidocs.intro.point2.description": {"message": "AI写作助手，帮助生成、编辑和改进内容"}, "aidocs.intro.point3.name": {"message": "批判性思维提升"}, "aidocs.intro.point3.description": {"message": "批判性思维提升，识别偏见和逻辑谬误"}, "aidocs.intro.point4.name": {"message": "无缝集成"}, "aidocs.intro.point4.description": {"message": "与AIFlow思维导图和AI幻灯片无缝集成，形成一体化工作流"}, "aidocs.intro.point5.name": {"message": "统一的知识工作空间"}, "aidocs.intro.point5.description": {"message": "通过文档、幻灯片和思维导图之间的链接，形成统一的知识工作空间"}, "aidocs.features.title": {"message": "主要功能"}, "aidocs.features.item1.name": {"message": "基于块的编辑器"}, "aidocs.features.item1.description": {"message": "使用直观界面创建美观的文档，无缝混合文本、图像、表格、代码块等。"}, "aidocs.features.item2.name": {"message": "AI写作助手"}, "aidocs.features.item2.description": {"message": "从零开始生成完整的文档，完善现有内容，或智能建议改进写作。"}, "aidocs.features.item3.name": {"message": "信息图表生成器"}, "aidocs.features.item3.description": {"message": "选择文档中的文本，让AI助手自动生成信息图表、流程图、幻灯片等，并嵌入到当前位置。"}, "aidocs.features.item4.name": {"message": "批判性思维提升"}, "aidocs.features.item4.description": {"message": "使用AI工具识别写作中的认知偏见、逻辑谬误和弱论点。"}, "aidocs.features.item5.name": {"message": "文档组织"}, "aidocs.features.item5.description": {"message": "使用嵌套页面、文档间内部链接和集成项目管理系统创建结构化工作空间。"}, "aidocs.ai-assistant.title": {"message": "AI写作助手：您的思考伙伴"}, "aidocs.ai-assistant.subtitle": {"message": "不仅是一款写作工具，更是一款提升您批判性思维的AI"}, "aidocs.ai-assistant.critical-thinking.title": {"message": "提升批判性思维"}, "aidocs.ai-assistant.critical-thinking.description": {"message": "FunBlocks AI Docs不仅仅是修正语法和风格，还能帮助您更清晰地思考，更有说服力地写作。"}, "aidocs.ai-assistant.critical-thinking.point1": {"message": "识别写作中的认知偏见"}, "aidocs.ai-assistant.critical-thinking.point2": {"message": "突见逻辑谬误并提出改进建议"}, "aidocs.ai-assistant.critical-thinking.point3": {"message": "通过处理反对意见来加强论点"}, "aidocs.ai-assistant.critical-thinking.point4": {"message": "分析您的推理的清晰度和连贯性"}, "aidocs.writing-assistant.title": {"message": "智能写作工具"}, "aidocs.writing-assistant.description": {"message": "使用强大的AI写作工具高效地创建高质量内容。"}, "aidocs.writing-assistant.point1": {"message": "从简单主题或大纲生成完整的文档"}, "aidocs.writing-assistant.point2": {"message": "根据具体指导智能重写和完善选定的文本"}, "aidocs.writing-assistant.point3": {"message": "根据您的写作风格提供智能的续写建议"}, "aidocs.writing-assistant.point4": {"message": "提供详细的语法和风格纠正"}, "aidocs.writing-assistant.point5": {"message": "根据内容上下文自动创建信息图表、流程图和数据图表等视觉辅助工具"}, "aidocs.ecosystem.title": {"message": "无缝FunBlocks生态系统集成"}, "aidocs.ecosystem.subtitle": {"message": "作为强大的AI工作空间的一部分，提升您的整个工作流程"}, "aidocs.ecosystem.workflow.title": {"message": "完整的工作流程集成"}, "aidocs.ecosystem.workflow.point1": {"message": "一键将AIFlow思维导图直接转换为结构化文档"}, "aidocs.ecosystem.workflow.point2": {"message": "将任何文档快速转换为专业的幻灯片"}, "aidocs.ecosystem.workflow.point3": {"message": "将文档内容快速转换为视觉思维导图，进行更深入的探索"}, "aidocs.ecosystem.workflow.point4": {"message": "根据选定的文本创建信息图表、流程图和数据图表，并将其插入到文档中"}, "aidocs.ecosystem.workflow.point5": {"message": "通过文档、思维导图和幻灯片的双向链接创建一个全面的工作空间"}, "aidocs.organization.title": {"message": "强大的文档组织"}, "aidocs.organization.description": {"message": "通过链接文档和层次化组织创建结构化工作空间"}, "aidocs.organization.point1.name": {"message": "层次化结构"}, "aidocs.organization.point1.description": {"message": "使用无限嵌套的文件夹和子文件夹直观地组织您的文档，保持工作空间的清洁和直观。"}, "aidocs.organization.point2.name": {"message": "内部链接"}, "aidocs.organization.point2.description": {"message": "通过内部链接连接相关的文档、思维导图和幻灯片，创建一个可导航的知识网络。"}, "aidocs.organization.point3.name": {"message": "混合内容类型"}, "aidocs.organization.point3.description": {"message": "在一个工作空间中结合文档页面、思维导图、信息图表、流程图和幻灯片，获得一个完整的项目视图。"}, "aidocs.organization.point4.name": {"message": "知识工作空间"}, "aidocs.organization.point4.description": {"message": "通过链接文档、思维导图和幻灯片，整合工作成果，构建个人或团队的AI知识库。"}, "aidocs.use-cases.title": {"message": "使用案例"}, "aidocs.use-cases.description": {"message": "FunBlocks AI Docs适应多种内容创作场景，提升生产效率和质量"}, "aidocs.use-cases.case1.title": {"message": "学术研究和论文"}, "aidocs.use-cases.case1.description": {"message": "创建结构良好的学术论文，具有强有力的论点和适当的引用，AI帮助识别论证中的潜在缺陷。"}, "aidocs.use-cases.case2.title": {"message": "专业内容创作"}, "aidocs.use-cases.case2.description": {"message": "使用AI助手保持一致的语调和引人入胜的叙述，生产高质量的博客、文章和报告。"}, "aidocs.use-cases.case3.title": {"message": "学习和笔记"}, "aidocs.use-cases.case3.description": {"message": "通过AI增强的组织和关键概念的扩展，将课堂笔记转化为全面的学习材料。"}, "aidocs.use-cases.case4.title": {"message": "商业文档"}, "aidocs.use-cases.case4.description": {"message": "开发清晰的商业计划、提案和报告，逻辑地呈现想法并解决潜在的异议。"}, "aidocs.use-cases.case5.title": {"message": "项目规划和管理"}, "aidocs.use-cases.case5.description": {"message": "创建连接的工作空间，结合规划文档、思维导图和幻灯片，实现团队对齐。"}, "aidocs.use-cases.case6.title": {"message": "批判性思维发展"}, "aidocs.use-cases.case6.description": {"message": "通过定期反馈，帮助您识别和纠正错误的思维模式，提高推理能力。"}, "aidocs.testimonials.user1.name": {"message": "张艾米莉"}, "aidocs.testimonials.user1.role": {"message": "学术研究员"}, "aidocs.testimonials.user1.text": {"message": "FunBlocks AI Docs彻底改变了我的研究过程。批判性思维提升功能帮助我识别了我在学术论文中可能会忽视的潜在偏见。我可以无缝地将思维导图中的初始想法转化为结构化文档，然后转化为会议演示幻灯片——所有在一个生态系统中完成。AI写作助手帮助我完善论点并解决反对意见，使我的研究论文更加严谨。自从使用这个工具以来，我的系上同事们对我的工作变得更加全面和逻辑严谨感到惊讶。"}, "aidocs.testimonials.user2.name": {"message": "卡洛斯·门德斯"}, "aidocs.testimonials.user2.role": {"message": "内容创作者"}, "aidocs.testimonials.user2.text": {"message": "作为一个每周产出多篇文章的人，FunBlocks AI Docs改变了我的工作方式。块编辑器使得组织内容变得直观，而AI写作助手帮助我立即克服了写作障碍。我最欣赏的是它通过突见逻辑谬误来加强我的论点——现在我的内容更有说服力和吸引力。与思维导图和幻灯片的无缝集成意味着我可以在不同格式之间重新利用我的写作内容，节省大量工作时间。这个工具不仅帮助我更快地写作，还帮助我更深入地思考，创作更好的内容。"}, "aidocs.testimonials.user3.name": {"message": "莎拉·约翰逊"}, "aidocs.testimonials.user3.role": {"message": "商业顾问"}, "aidocs.testimonials.user3.text": {"message": "FunBlocks AI Docs彻底改变了我创建客户提案和报告的方式。层次化的文档组织功能使得我所有的项目材料都能整齐地结构化，而内部链接功能创建了一个全面的知识库，我可以在未来的工作中参考。在制定商业策略时，批判性思维提升功能帮助我发现我可能会做的假设，从而进行更全面的分析。自从使用这个工具以来，我收到了客户对我的建议的清晰和说服力的赞扬，这些我归功于AI帮助我加强论点并解决潜在的异议。"}, "aidocs.testimonials.user4.name": {"message": "刘伟"}, "aidocs.testimonials.user4.role": {"message": "研究生"}, "aidocs.testimonials.user4.text": {"message": "在讲座中记笔记曾经是一件混乱的事情，但是有了FunBlocks AI Docs，我可以快速在块编辑器中组织我的思维，并让AI帮助我扩展我可能错过的关键概念。将笔记转化为思维导图的能力帮助我看到不同主题之间的联系，这对全面考试准备非常有价值。批判性思维功能极大地提高了我的论文写作水平——我的教授评论说，我的论点变得更加连贯和合理。这个工具不仅真正提高了我的成绩，还改变了我对学习的方式。"}, "aidocs.testimonials.user5.name": {"message": "普里娅·帕特尔"}, "aidocs.testimonials.user5.role": {"message": "项目经理"}, "aidocs.testimonials.user5.text": {"message": "管理复杂的多方利益相关者项目曾经是一场文档噩梦，直到我发现了FunBlocks AI Docs。统一的工作空间让我能够链接相关文档、用于头脑风暴的思维导图以及用于利益相关者会议的演示幻灯片——所有内容相互关联。AI帮助我起草清晰的项目计划，并通过分析我提案中的逻辑来预见潜在问题。团队沟通显著改善，因为我们现在有一个集中知识库，所有人都可以访问项目信息。这个工具不仅仅是组织信息；它增强了我们的集体思维过程。"}, "aidocs.testimonials.user6.name": {"message": "丹尼尔·科瓦尔斯基"}, "aidocs.testimonials.user6.role": {"message": "创意作家"}, "aidocs.testimonials.user6.text": {"message": "使FunBlocks AI Docs与其他写作工具不同的是，它如何增强我的创作过程，而不仅仅是纠正语法。AI写作助手提供智能的续写建议，符合我的风格，同时引入我未曾考虑的新视角。块编辑器让我可以自由地重新排列段落，随着叙事的发展而变化。在发展角色和情节时，我使用思维导图集成来可视化关系，然后将这些想法转化为完整的场景。对于重视创作自由和结构指导的人来说，这个工具提供了完美的平衡。"}, "aidocs.cta.title": {"message": "转变你的思维，提升你的写作"}, "aidocs.cta.subtitle": {"message": "加入成千上万的专业人士，利用FunBlocks AI Docs提升他们的创造力、生产力和批判性思维"}, "aidocs.cta.button": {"message": "立即体验更智能的写作"}, "aidocs.faq.q1": {"message": "什么是FunBlocks AI Docs，它如何改善文档写作和编辑？"}, "aidocs.faq.a1": {"message": "FunBlocks AI Docs是一个全面的AI驱动文档编辑器，结合了类似Notion的块编辑和先进的人工智能，以增强您的写作过程、批判性思维能力和整体生产力。该平台具有类似Notion的灵活块编辑器、强大的AI写作辅助、识别偏见和逻辑谬误的批判性思维增强工具，以及与其他FunBlocks产品（如AIFlow思维导图和AI Slides）的无缝集成。无论您是在创建学术论文、商业文档还是创意内容，FunBlocks AI Docs都提供了更智能的写作和更深入的思考工具。"}, "aidocs.faq.q2": {"message": "FunBlocks AI Docs中的AI写作助手如何帮助提高我的写作质量和效率？"}, "aidocs.faq.a2": {"message": "FunBlocks AI Docs中的AI写作助手充当您的个人思维伙伴，以提升写作质量和效率。它提供四个关键功能：1）根据简单主题或大纲从头生成完整文档，2）根据特定指令智能重写和完善选定文本，3）提供符合您个人写作风格的智能续写建议，4）提供全面的语法和风格修正，并附有详细解释。与仅修正语法的基本写作工具不同，FunBlocks AI写作助手帮助您发展更清晰的思维、更有说服力的论点和更高质量的内容，同时显著减少写作时间。"}, "aidocs.faq.q3": {"message": "FunBlocks AI Docs中的批判性思维增强功能与其他写作工具相比有什么独特之处？"}, "aidocs.faq.a3": {"message": "FunBlocks AI Docs中的批判性思维增强功能远远超出了传统写作工具的提供，专注于您的推理质量，而不仅仅是写作技巧。这个独特的功能通过四个高级能力帮助您更清晰地思考和更有说服力地写作：1）识别您写作中可能削弱论点的特定认知偏见，2）突出逻辑谬误并提供明确的改进建议，3）通过识别和解决潜在反对意见来加强您的论点，4）对您推理的清晰度和连贯性进行深入分析。这个功能本质上充当了批判性思维教练，帮助您发展更强的分析能力，同时产生更具逻辑性的内容。"}, "aidocs.faq.q4": {"message": "FunBlocks AI Docs如何与AIFlow思维导图和AI Slides集成以创建完整的工作流程系统？"}, "aidocs.faq.a4": {"message": "FunBlocks AI Docs通过全面的生态系统方法与AIFlow思维导图和AI Slides深度集成，创建无缝的一体化工作流程。此集成使四个强大的工作流程能力成为可能：1）一键将AIFlow思维导图直接转换为具有适当层次和组织结构的文档，2）自动将任何文档转换为适合会议或会议的专业演示幻灯片，3）快速将文档内容转换为可视化思维导图，以便概念探索和关系映射，4）通过文档、思维导图和演示文稿之间的双向链接创建统一的知识工作空间。这个互联系统消除了在不同应用程序之间切换的需要，使思想能够自然地在不同格式之间流动，同时保持整个项目内容的一致性。"}, "aidocs.faq.q5": {"message": "FunBlocks AI Docs为个人和团队使用提供了哪些文档组织和知识管理功能？"}, "aidocs.faq.a5": {"message": "FunBlocks AI Docs提供了一整套文档组织和知识管理功能，旨在提高个人生产力和团队协作。该系统包括：1）具有无限嵌套文件夹和子文件夹的高级层次结构，以便直观地分类内容，2）强大的内部链接功能，连接相关文档、思维导图和演示文稿，创建可导航的知识网络，3）支持在单个工作区内混合内容类型，允许您在一个视图中组合文档页面、思维导图和演示文稿，4）知识工作区功能，整合工作成果以构建个人或团队的AI知识库。这些功能结合在一起，创建了一个结构化但灵活的系统，随着您的项目发展而增长，并保持信息的可访问性和上下文连接。"}, "aidocs.faq.q6": {"message": "学术界和研究人员如何使用FunBlocks AI Docs来改善研究论文和学术写作？"}, "aidocs.faq.a6": {"message": "学术界和研究人员可以利用FunBlocks AI Docs显著提升他们的学术写作过程和研究论文质量。该平台帮助创建结构良好的学术论文，具有逻辑严谨的论点和适当的引用，同时AI系统地识别推理或方法论中的潜在缺陷。研究人员受益于批判性思维增强功能，能够捕捉学术写作中常见的认知偏见和逻辑谬误。思维导图集成帮助可视化复杂的研究关系和理论框架，然后将其转化为正式的文档结构。此外，将研究结果转化为演示幻灯片的能力简化了会议准备。正如学术用户艾米莉·张所确认的，该平台在制作更全面和逻辑严谨的研究论文方面特别有价值，得到了学术同行的积极认可。"}, "aidocs.faq.q7": {"message": "使用FunBlocks AI Docs可以创建哪些类型的商业文档，它如何改善商业写作？"}, "aidocs.faq.a7": {"message": "FunBlocks AI Docs非常适合创建各种商业文档，包括商业计划、项目提案、市场分析报告、战略建议、客户演示和内部流程文档。该平台通过帮助专业人士开发清晰、逻辑的文档，提升商业写作，能够有说服力地呈现想法，并主动应对潜在的反对意见或缺陷。统一的工作空间通过结合战略规划文档、用于头脑风暴的可视化思维导图和利益相关者对齐的演示材料，促进全面的项目规划和管理。像商业顾问莎拉·约翰逊这样的商业用户报告说，自从使用该平台以来，他们的建议在清晰性和说服力方面得到了客户的赞赏。批判性思维增强功能在商业战略发展中尤其有价值，因为它帮助识别未表述的假设和逻辑缺口，这些可能会削弱商业提案。"}, "aidocs.faq.q8": {"message": "FunBlocks AI Docs中的Notion风格块编辑器是如何工作的，支持哪些内容类型？"}, "aidocs.faq.a8": {"message": "FunBlocks AI Docs中的Notion风格块编辑器提供了一个直观、灵活的文档创建界面，通过模块化的构建块方法。每个内容块都是一个独立的块，可以轻松移动、编辑或转换。该编辑器支持多种内容类型，包括具有多种格式选项的富文本、标题和副标题、项目符号和编号列表、具有排序和过滤功能的表格、嵌入的图像和媒体、具有多种编程语言语法高亮的代码块、数学方程、重要信息的提示块、可展开内容的切换列表和嵌入文件。这个基于块的方法允许通过拖放功能轻松重新组织，确保文档的一致格式，并能够快速在块类型之间转换。该系统结合了文字处理器的灵活性和数据库的结构性，非常适合创建任何复杂程度的精美组织文档。"}, "aidocs.faq.q9": {"message": "学生和终身学习者如何使用FunBlocks AI Docs来改善笔记、学习材料和知识保留？"}, "aidocs.faq.a9": {"message": "学生和终身学习者可以通过FunBlocks AI Docs的多个强大学习应用来转变他们的教育体验。该平台在帮助用户将快速的讲座笔记转化为全面的学习材料方面表现出色，通过AI增强的组织和智能扩展关键概念。像魏刘这样的学生发现，将线性笔记转化为可视化思维导图有助于识别不同主题之间的联系，显著改善考试准备和概念理解。块编辑器允许灵活组织学习材料，支持混合媒体类型，而AI助手可以帮助澄清困难概念或生成复杂主题的解释。批判性思维功能在论文写作中尤其有价值，帮助学生发展更连贯、推理更充分的论点，教授们注意到并给予奖励。除了正式教育，该平台还支持终身学习，通过创建一个随着时间推移而增长的结构化知识库，连接不同学科之间的相关概念，并识别可能被忽视的模式。"}, "aidocs.faq.q10": {"message": "FunBlocks AI Docs是否提供免费试用，不同定价计划中包含哪些功能？"}, "aidocs.faq.a10": {"message": "是的，FunBlocks AI Docs提供免费试用版本，网站上显著的\"免费试用\"号召性用语表明了这一点。此试用允许新用户体验平台的核心功能，并评估其如何满足他们的写作和思维需求，然后再决定是否订阅付费版本。虽然当前信息中未提供具体的定价细节，但免费试用可能包括对基本块编辑功能的访问、有限的AI写作辅助和批判性思维增强功能的样本。有关不同定价层、免费版本中的功能限制、使用配额和企业选项的全面信息，我们建议访问官方FunBlocks定价页面。与许多生产力工具一样，付费订阅可能提供额外的好处，例如增加使用限制、优先支持、团队协作功能和访问高级AI能力。"}, "aidocs.faq.q11": {"message": "FunBlocks AI Docs如何帮助内容创作者、博主和专业作家提高内容质量和生产效率？"}, "aidocs.faq.a11": {"message": "FunBlocks AI Docs为内容创作者、博主和专业作家提供了显著的优势，他们需要在生产中保持质量和数量。正如专业内容创作者卡洛斯·门德斯所经历的，该平台帮助生成高质量的博客、文章、报告和创意内容，AI辅助保持一致的语调，并在多篇作品中创造引人入胜的叙事。AI写作助手在克服写作障碍方面尤其有价值，通过智能建议和草稿生成，而批判性思维功能通过突出逻辑谬误来增强论点，使内容对读者更具说服力和吸引力。内容专业人士受益于能够快速在不同格式（文档、思维导图和演示文稿）之间重新利用书面内容，显著减少多渠道内容策略的生产时间。块编辑器的直观组织使管理多个相关内容变得更简单，而像丹尼尔·科瓦尔斯基这样的创意作家欣赏该系统通过提供符合他们独特风格的续写建议，同时引入他们未曾考虑的新视角，来增强创作过程。"}, "aidocs.faq.q12": {"message": "我如何开始使用FunBlocks AI Docs？"}, "aidocs.faq.a12": {"message": "只需点击\"免费试用\"按钮，创建一个帐户，您就可以立即免费使用FunBlocks AI。试用不需要信用卡，您将可以访问所有功能。"}, "homepage.thinking_matters.title": {"message": "在人工智能时代，您的思考很重要"}, "homepage.thinking_matters.description": {"message": "在当今充满AI工具的快节奏世界中，有效的思考能力比以往任何时候都更加重要。FunBlocks通过结构化思考方法和可视化帮助您增强认知能力。"}, "homepage.thinking_matters.master.title": {"message": "掌控您的思维"}, "homepage.thinking_matters.master.description": {"message": "在当今信息丰富的世界中，有效的思考方法至关重要。FunBlocks AIFlow通过将经过验证的认知技术与直观的技术相结合，帮助您消除噪音，获得清晰度，提高效率。"}, "homepage.thinking_matters.concepts.title": {"message": "核心思维工具"}, "homepage.thinking_matters.concepts.description": {"message": "我们的平台建立在最本质的思维方法和工具之上：头脑风暴用于生成想法，思维导图用于组织，批判性思维用于分析，创造性思维用于创新，思维模型用于理解。"}, "homepage.thinking_matters.empowerment.title": {"message": "AI增强思考"}, "homepage.thinking_matters.empowerment.description": {"message": "FunBlocks AIFlow通过统一的工作空间、可视化工具、AI辅助和无缝协作功能增强您的思维能力—所有这些都旨在帮助您更深入、更有效地思考。"}, "homepage.thinking_matters.learn_more": {"message": "了解更多关于我们的思考工具箱"}, "aiflow.thinking_methods.brainstorming": {"message": "头脑风暴"}, "aiflow.thinking_methods.mind_mapping": {"message": "思维导图"}, "aiflow.thinking_methods.critical_thinking": {"message": "批判性思维"}, "aiflow.thinking_methods.creative_thinking": {"message": "创造性思维"}, "aiflow.thinking-methods.title": {"message": "通过AIFlow增强您的思维能力"}, "aiflow.thinking-methods.description": {"message": "FunBlocks AIFlow将强大的思维方法与AI集成，提升您的认知能力"}, "aiflow.thinking-methods.aiflow-features": {"message": "AIFlow功能："}, "aiflow.thinking-methods.brainstorming.title": {"message": "头脑风暴"}, "aiflow.thinking-methods.brainstorming.description": {"message": "不带判断地生成广泛的想法"}, "aiflow.thinking-methods.brainstorming.feature1": {"message": "AI辅助的想法生成"}, "aiflow.thinking-methods.brainstorming.feature2": {"message": "经典思维模型集成"}, "aiflow.thinking-methods.brainstorming.feature3": {"message": "想法的可视化组织"}, "aiflow.thinking-methods.brainstorming.feature4": {"message": "概念的一键扩展"}, "aiflow.thinking-methods.mind_mapping.title": {"message": "思维导图"}, "aiflow.thinking-methods.mind_mapping.description": {"message": "以可视化方式组织信息，查看连接"}, "aiflow.thinking-methods.mind_mapping.feature1": {"message": "用于复杂图的无限画布"}, "aiflow.thinking-methods.mind_mapping.feature2": {"message": "层次节点结构"}, "aiflow.thinking-methods.mind_mapping.feature3": {"message": "AI生成的连接"}, "aiflow.thinking-methods.mind_mapping.feature4": {"message": "视觉自定义选项"}, "aiflow.thinking-methods.critical_thinking.title": {"message": "批判性思维"}, "aiflow.thinking-methods.critical_thinking.description": {"message": "客观分析信息以形成合理判断"}, "aiflow.thinking-methods.critical_thinking.feature1": {"message": "认知偏见识别"}, "aiflow.thinking-methods.critical_thinking.feature2": {"message": "假设测试工具"}, "aiflow.thinking-methods.critical_thinking.feature3": {"message": "多角度分析"}, "aiflow.thinking-methods.critical_thinking.feature4": {"message": "结构化评估框架"}, "aiflow.thinking-methods.creative_thinking.title": {"message": "创造性思维"}, "aiflow.thinking-methods.creative_thinking.description": {"message": "开发创新解决方案和独特视角"}, "aiflow.thinking-methods.creative_thinking.feature1": {"message": "横向思维提示"}, "aiflow.thinking-methods.creative_thinking.feature2": {"message": "类比推理工具"}, "aiflow.thinking-methods.creative_thinking.feature3": {"message": "约束移除练习"}, "aiflow.thinking-methods.creative_thinking.feature4": {"message": "想法组合技术"}, "aiflow.case-studies.title": {"message": "AIFlow实践：真实应用案例"}, "aiflow.case-studies.description": {"message": "了解专业人士和学生如何使用FunBlocks AIFlow提升思维和生产力"}, "aiflow.case-studies.education.description": {"message": "一位大学教授使用AIFlow帮助学生可视化认知心理学中的复杂概念。学生创建思维导图来连接理论、研究发现和实际应用，与传统笔记相比，理解力提高了40%。"}, "aiflow.case-studies.business.title": {"message": "商业创新"}, "aiflow.case-studies.business.description": {"message": "一家科技初创公司的产品开发团队使用AIFlow的头脑风暴和批判性思维工具来识别市场空白并开发创新解决方案。可视化方法帮助他们将规划时间减少60%，同时产生3倍以上的可行产品创意。"}, "aiflow.educational-resources.title": {"message": "教育资源"}, "aiflow.educational-resources.description": {"message": "通过我们全面的指南和教程提升您的思维技能"}, "aiflow.educational-resources.learn_more": {"message": "了解更多"}, "aiflow.educational-resources.resource1.title": {"message": "头脑风暴技巧"}, "aiflow.educational-resources.resource1.description": {"message": "学习由AI增强的有效头脑风暴方法"}, "aiflow.educational-resources.resource2.title": {"message": "思维导图精通"}, "aiflow.educational-resources.resource2.description": {"message": "探索如何为任何目的创建强大的思维导图"}, "aiflow.educational-resources.resource3.title": {"message": "批判性思维技能"}, "aiflow.educational-resources.resource3.description": {"message": "通过结构化方法增强您的分析能力"}, "aiflow.educational-resources.resource4.title": {"message": "创造性问题解决"}, "aiflow.educational-resources.resource4.description": {"message": "通过创新技术突破创意障碍"}, "aiflow.educational-resources.resource5.title": {"message": "思维模型库"}, "aiflow.educational-resources.resource5.description": {"message": "获取用于复杂问题的强大思维框架"}, "aiflow.educational-resources.resource6.title": {"message": "集成工作流指南"}, "aiflow.educational-resources.resource6.description": {"message": "了解所有思维方法如何在AIFlow中协同工作"}, "aiflow.comparison.title": {"message": "FunBlocks AIFlow与其他工具的比较"}, "aiflow.comparison.description": {"message": "了解FunBlocks AIFlow如何在思维工具领域脱颖而出"}, "aiflow.comparison.feature1": {"message": "头脑风暴能力"}, "aiflow.comparison.feature2": {"message": "思维导图"}, "aiflow.comparison.feature3": {"message": "批判性思维工具"}, "aiflow.comparison.feature4": {"message": "创造性思维框架"}, "aiflow.comparison.feature5": {"message": "可视化思维集成"}, "aiflow.comparison.advanced": {"message": "✅ 高级"}, "aiflow.comparison.basic": {"message": "✅ 基础"}, "aiflow.comparison.no": {"message": "❌"}, "aiflow.comparison.note": {"message": "FunBlocks AIFlow集成了各种思维方法和AI支持，特别擅长高级头脑风暴、思维导图、批判性思维和视觉集成，相比传统AI聊天机器人和通用思维导图或文档工具更胜一筹。"}, "aidocs.head.title": {"message": "FunBlocks AI 文档：AI驱动的块编辑器和批判性思维助手 | 头脑风暴与思维导图"}, "aidocs.head.description": {"message": "FunBlocks AI 文档结合了类似Notion的编辑功能和AI辅助，以提升您的写作、批判性思维和头脑风暴能力。通过集成思维导图和创造性思维工具，更快地创建更好的内容。"}, "aidocs.comparison.title": {"message": "FunBlocks AI文档的优势对比"}, "aidocs.comparison.description": {"message": "FunBlocks AI文档将文档编辑器、AI写作助手和创意思维工具的最佳功能结合到一个无缝平台中"}, "aidocs.comparison.funblocksHeader": {"message": "FunBlocks AI文档"}, "aidocs.comparison.googleDocsHeader": {"message": "Google文档"}, "aidocs.comparison.notionHeader": {"message": "Notion"}, "aidocs.comparison.grammarly": {"message": "Grammarly"}, "aidocs.comparison.feature1": {"message": "基于块的编辑器"}, "aidocs.comparison.feature2": {"message": "AI写作助手"}, "aidocs.comparison.feature3": {"message": "批判性思维增强"}, "aidocs.comparison.feature4": {"message": "头脑风暴工具"}, "aidocs.comparison.feature5": {"message": "思维导图集成"}, "aidocs.comparison.feature6": {"message": "信息图表生成"}, "aidocs.comparison.feature7": {"message": "创造性思维工具"}, "aidocs.comparison.feature8": {"message": "文档组织"}, "aidocs.comparison.note": {"message": "FunBlocks AI文档提供了一个综合解决方案，将文档编辑、AI辅助和创造性思维工具结合在一个平台中，无需在多个应用程序之间切换。"}, "aidocs.stats.title": {"message": "全球专业人士的信赖之选"}, "aidocs.stats.description": {"message": "加入数千名使用FunBlocks AI文档提升写作和思考能力的专业人士"}, "aidocs.stats.users": {"message": "活跃用户"}, "aidocs.stats.documents": {"message": "已创建文档"}, "aidocs.stats.timeSaved": {"message": "平均节省时间"}, "aidocs.stats.satisfaction": {"message": "用户满意度"}, "aidocs.thinking-enhancement.title": {"message": "写作作为思维工具"}, "aidocs.thinking-enhancement.description": {"message": "写作不仅仅是为了输出—它是一种强大的学习方法和增强认知能力的途径。FunBlocks AI生态系统为您的思维之旅提供完整解决方案。"}, "aidocs.thinking-enhancement.cycle.title": {"message": "完整的思维循环"}, "aidocs.thinking-enhancement.cycle.description": {"message": "FunBlocks AI整合了思维过程的所有阶段—从初始想法到结构化知识—创建了一个强大的学习和智力成长生态系统。"}, "aidocs.thinking-enhancement.cycle.step1.title": {"message": "探索与发现"}, "aidocs.thinking-enhancement.cycle.step1.description": {"message": "从AI驱动的头脑风暴开始，探索主题并生成初始想法"}, "aidocs.thinking-enhancement.cycle.step2.title": {"message": "可视化与连接"}, "aidocs.thinking-enhancement.cycle.step2.description": {"message": "通过思维导图组织思想，查看关系并识别模式"}, "aidocs.thinking-enhancement.cycle.step3.title": {"message": "创建与发展"}, "aidocs.thinking-enhancement.cycle.step3.description": {"message": "利用创造性思维工具将想法转化为结构化文档"}, "aidocs.thinking-enhancement.cycle.step4.title": {"message": "分析与完善"}, "aidocs.thinking-enhancement.cycle.step4.description": {"message": "应用批判性思维来加强论点并消除偏见"}, "aidocs.thinking-enhancement.tools.title": {"message": "AI驱动的思维工具"}, "aidocs.thinking-enhancement.tools.brainstorming": {"message": "使用AI引导的框架（如SCAMPER、六顶思考帽和第一性原理思维）生成想法。将种子概念扩展为全面的大纲。"}, "aidocs.thinking-enhancement.tools.mindmap": {"message": "通过文档和思维导图之间的一键转换，可视化想法之间的复杂关系。通过视觉思维增强理解和记忆。"}, "aidocs.thinking-enhancement.tools.creative": {"message": "从多个角度查看内容，生成类比和隐喻，挑战限制性假设，发现创新解决方案和见解。"}, "aidocs.thinking-enhancement.tools.critical.title": {"message": "批判性思维助手"}, "aidocs.thinking-enhancement.tools.critical": {"message": "识别认知偏见，突出逻辑谬误，通过解决反对意见来加强论点，分析推理的清晰度。"}, "aidocs.thinking-enhancement.quote": {"message": "\"写作不仅仅是交流想法；它是发展想法的强大工具。FunBlocks AI将写作从单纯的记录转变为主动学习过程。\""}, "prompt_optimizer.less_is_more.title": {"message": "用更少的指令，获得更多AI帮助"}, "prompt_optimizer.less_is_more.description": {"message": "在AI提示中，有时少即是多。只需说明你的目标，让AI来完成剩下的工作。"}, "prompt_optimizer.less_is_more.point1.title": {"message": "专注于你的目标"}, "prompt_optimizer.less_is_more.point1.description": {"message": "与其提供详细指令，不如简单说明你想要实现的目标。让AI来分析和规划最佳方案。"}, "prompt_optimizer.less_is_more.point2.title": {"message": "突破限制"}, "prompt_optimizer.less_is_more.point2.description": {"message": "过于详细的指令可能会限制AI的创造力。通过关注目标，你打开了通向可能从未考虑过的解决方案的大门。"}, "prompt_optimizer.less_is_more.point3.title": {"message": "让AI承担重任"}, "prompt_optimizer.less_is_more.point3.description": {"message": "Prompt Optimizer帮助你从简单的目标开始，然后使用AI来扩展和增强你的提示，以获得更好的结果。"}, "prompt_optimizer.less_is_more.cta": {"message": "体验Prompt Optimizer如何让\"少即是多\"成为现实"}, "prompt_optimizer.less_is_more.button": {"message": "立即体验"}, "prompt_optimizer.lazy_prompting.title": {"message": "懒人提示法：AI交互的新方法"}, "prompt_optimizer.lazy_prompting.description": {"message": "懒人提示法是一种创新的方法，从最小上下文开始，根据需要逐步添加更多内容，让AI用意想不到的见解和解决方案给我们带来惊喜。"}, "prompt_optimizer.lazy_prompting.point1.title": {"message": "从简单开始"}, "prompt_optimizer.lazy_prompting.point1.description": {"message": "从一个基本想法或问题开始，让AI在没有详细指令限制的情况下探索可能性。"}, "prompt_optimizer.lazy_prompting.point2.title": {"message": "发现意外惊喜"}, "prompt_optimizer.lazy_prompting.point2.description": {"message": "让AI用传统提示方法可能想不到的解决方案和视角给你带来惊喜。"}, "prompt_optimizer.lazy_prompting.point3.title": {"message": "按需优化"}, "prompt_optimizer.lazy_prompting.point3.description": {"message": "当你需要更具体的指导或详细回答时，使用提示优化器来增强和完善你的提示。"}, "prompt_optimizer.lazy_prompting.cta": {"message": "解锁FunBlocks AI提示优化器的懒人提示法的力量。同时享受懒人提示法和高质量AI回答的好处。"}, "prompt_optimizer.lazy_prompting.button": {"message": "立即尝试"}, "prompt_optimizer.thinking_enhancement.title": {"message": "超越提示词优化：提升您的思维能力"}, "prompt_optimizer.thinking_enhancement.description": {"message": "我们的插件不仅仅优化提示词——它主动培养您的批判性思维技能，通过增强人机协作来最大化AI价值。在AI时代，思考能力至关重要。"}, "prompt_optimizer.thinking_enhancement.point1.title": {"message": "掌握提出更好问题的艺术"}, "prompt_optimizer.thinking_enhancement.point1.description": {"message": "好的问题是解决问题的关键。提问能力是人类重要的思考能力。我们的AI帮助您优化问题并通过引导式改进练习这项基本技能。"}, "prompt_optimizer.thinking_enhancement.point2.title": {"message": "更清晰的问题描述"}, "prompt_optimizer.thinking_enhancement.point2.description": {"message": "通过引导式优化学会更清晰、更精确地描述问题，培养有益于所有AI交互的更好沟通技能。"}, "prompt_optimizer.thinking_enhancement.point3.title": {"message": "更广阔的视角探索"}, "prompt_optimizer.thinking_enhancement.point3.description": {"message": "基于您的输入或AI回复生成相关问题和主题，鼓励您从多个角度探索主题并发现新见解。"}, "prompt_optimizer.thinking_enhancement.point4.title": {"message": "批判性分析技能"}, "prompt_optimizer.thinking_enhancement.point4.description": {"message": "对AI回复进行批判性分析，提高您的分析和判断能力，帮助您评估信息质量并识别潜在偏见。"}, "prompt_optimizer.thinking_enhancement.cta.title": {"message": "增强而非取代您的思考"}, "prompt_optimizer.thinking_enhancement.cta.description": {"message": "使用我们的插件后，您不仅能从ChatGPT、Claude等AI工具获得更好的结果，还会培养更强的思维能力。因为在AI时代，思考能力至关重要。"}, "prompt_optimizer.thinking_enhancement.cta.button": {"message": "开始与AI更好地思考"}, "ai101.hallucination.title": {"message": "LLM 幻觉"}, "ai101.hallucination.subtitle": {"message": "什么是幻觉？"}, "ai101.hallucination.definition.title": {"message": "定义"}, "ai101.hallucination.definition.content": {"message": "模型生成看似合理但实际上不准确或不存在的信息"}, "ai101.hallucination.types.title": {"message": "主要幻觉类型"}, "ai101.hallucination.types.factual.title": {"message": "事实幻觉"}, "ai101.hallucination.types.factual.item1": {"message": "虚假信息：生成不存在的历史事件、人物或数据"}, "ai101.hallucination.types.factual.item2": {"message": "假引文：捏造不存在的学术论文、网站链接"}, "ai101.hallucination.types.factual.item3": {"message": "数值错误：提供不正确的统计数据、日期、数量"}, "ai101.hallucination.types.logical.title": {"message": "逻辑幻觉"}, "ai101.hallucination.types.logical.item1": {"message": "推理错误：逻辑推理中的谬误"}, "ai101.hallucination.types.logical.item2": {"message": "因果混淆：错误地确立因果关系"}, "ai101.hallucination.types.logical.item3": {"message": "自相矛盾：同一响应中的矛盾性陈述"}, "ai101.hallucination.types.creative.title": {"message": "创造性幻觉"}, "ai101.hallucination.types.creative.item1": {"message": "虚构内容：创造不存在的故事、人物、作品"}, "ai101.hallucination.types.creative.item2": {"message": "混合信息：错误地结合来自不同来源的信息"}, "ai101.hallucination.causes.title": {"message": "原因"}, "ai101.hallucination.causes.training.title": {"message": "训练数据问题"}, "ai101.hallucination.causes.training.item1": {"message": "训练数据中的错误"}, "ai101.hallucination.causes.training.item2": {"message": "不完整的训练覆盖"}, "ai101.hallucination.causes.training.item3": {"message": "过时或相互矛盾的信息"}, "ai101.hallucination.causes.model.title": {"message": "模型机制限制"}, "ai101.hallucination.causes.model.item1": {"message": "基于概率的生成"}, "ai101.hallucination.causes.model.item2": {"message": "缺乏现实世界知识验证"}, "ai101.hallucination.causes.model.item3": {"message": "上下文理解限制"}, "ai101.hallucination.strategies.title": {"message": "识别和预防策略"}, "ai101.hallucination.strategies.user.title": {"message": "用户级"}, "ai101.hallucination.strategies.user.item1": {"message": "交叉验证：从多个来源验证重要信息"}, "ai101.hallucination.strategies.user.item2": {"message": "批判性思维：特别是对于特定数据，保持怀疑"}, "ai101.hallucination.strategies.user.item3": {"message": "专业判断：在专业领域依靠权威资源"}, "ai101.hallucination.strategies.technical.title": {"message": "技术级"}, "ai101.hallucination.strategies.technical.item1": {"message": "检索增强生成（RAG）：与实时知识库结合"}, "ai101.hallucination.strategies.technical.item2": {"message": "多模型验证：使用多个模型进行交叉验证"}, "ai101.hallucination.strategies.technical.item3": {"message": "置信度评估：标记答案可靠性"}, "ai101.hallucination.keyPoints.title": {"message": "要点"}, "ai101.hallucination.keyPoints.content": {"message": "🚨 记住：大型语言模型是强大的工具，但需要人类判断和验证以确保信息准确性"}, "ai101.workTransformation.title": {"message": "大变革：AI重塑工作与生活"}, "ai101.workTransformation.subtitle": {"message": "范式转移：从\"岗位替代\"到\"任务重构\""}, "ai101.workTransformation.globalChange.title": {"message": "全球劳动力市场剧变"}, "ai101.workTransformation.globalChange.content": {"message": "核心转变：从\"职业\"到\"任务\" - AI自动化的是工作中的具体\"任务\"（约占30%-70%），而非整个\"职业\"。"}, "ai101.workTransformation.aiPlusX.title": {"message": "AI+X复合型人才"}, "ai101.workTransformation.aiPlusX.content": {"message": "催生了\"AI+X\"复合型人才的需求，强调终身学习以维持职业韧性。"}, "ai101.workTransformation.example.title": {"message": "法律行业案例"}, "ai101.workTransformation.example.content": {"message": "AI自动化：法律研究、文件审阅、合同起草\n↓\n律师专注：复杂诉讼策略、客户共情、伦理推理、创造性解决方案\n↓\n结果：职业发展路径从线性阶梯变为动态\"技能晶格\""}, "ai101.humanCompetencies.title": {"message": "人类核心素养：后自动化世界的价值锚点"}, "ai101.humanCompetencies.subtitle": {"message": "价值转向：从\"做什么\"转向\"如何思考与协作\""}, "ai101.humanCompetencies.cognitive.title": {"message": "高阶认知能力"}, "ai101.humanCompetencies.cognitive.item1": {"message": "分析性思维、创造性思维"}, "ai101.humanCompetencies.cognitive.item2": {"message": "元认知技能和学习策略"}, "ai101.humanCompetencies.social.title": {"message": "社会情感能力"}, "ai101.humanCompetencies.social.item1": {"message": "领导力、协作力、沟通能力"}, "ai101.humanCompetencies.social.item2": {"message": "共情、说服和激励他人的能力"}, "ai101.humanCompetencies.personal.title": {"message": "个人特质"}, "ai101.humanCompetencies.personal.item1": {"message": "韧性、灵活性、敏捷性、自我驱动"}, "ai101.humanCompetencies.personal.item2": {"message": "AI与大数据素养"}, "ai101.humanCompetencies.metaphor.title": {"message": "核心隐喻：AI作为\"认知外骨骼\""}, "ai101.humanCompetencies.metaphor.content": {"message": "关系重构：从\"人机对立\"转向\"人机共生\"\n↓\n目标：将人类从重复性认知劳动中解放，专注于更高阶的创造与战略思考"}, "ai101.aiCapabilities.title": {"message": "新能力：AI增强人类成就"}, "ai101.aiCapabilities.subtitle": {"message": "增强引擎：拓展人类成就的新疆域"}, "ai101.aiCapabilities.science.title": {"message": "颠覆科学发现：从蛋白质到新材料"}, "ai101.aiCapabilities.science.item1": {"message": "AlphaFold：解决蛋白质折叠问题，预测超2亿种蛋白质结构，为全球节省约10亿年研究时间"}, "ai101.aiCapabilities.science.item2": {"message": "MatterGen：\"逆向设计\"新材料，AI在1小时内提出12万个候选结构"}, "ai101.aiCapabilities.science.item3": {"message": "科学家角色转变：从\"实验者\"到\"假说策划者\"与\"探究设计师\""}, "ai101.aiCapabilities.arts.title": {"message": "催生新文艺复兴：艺术、音乐与设计的伙伴"}, "ai101.aiCapabilities.arts.item1": {"message": "AI音乐 (MuseNet)：辅助创作、编曲，根据文本生成特定情绪音乐"}, "ai101.aiCapabilities.arts.item2": {"message": "AI绘画 (Midjourney)：降低视觉表达门槛，成为艺术家的\"创意加速器\""}, "ai101.aiCapabilities.arts.item3": {"message": "创作者价值转移：从\"技术执行\"到\"概念策划\"与\"审美判断\""}, "ai101.aiCapabilities.personalization.title": {"message": "个性化世界：从精准医疗到定制化体验"}, "ai101.aiCapabilities.personalization.item1": {"message": "精准医疗：基于个人基因、影像数据，提供定制化治疗方案"}, "ai101.aiCapabilities.personalization.item2": {"message": "定制化消费：电商、流媒体为每个用户打造独一无二的推荐与体验"}, "ai101.aiCapabilities.personalization.item3": {"message": "自适应学习：根据学生进度动态调整教学内容，实现\"因材施教\""}, "ai101.educationalRevolution.title": {"message": "再思考：AI时代的教育革命"}, "ai101.educationalRevolution.subtitle": {"message": "教育的文艺复兴：目标、内容与方法的全面重塑"}, "ai101.educationalRevolution.newGoals.title": {"message": "新的北极星：重新定义教育目的"}, "ai101.educationalRevolution.newGoals.item1": {"message": "从\"知识传授\"到\"素养培育\"：当知识唾手可得，教育回归\"立德树人\"的本质"}, "ai101.educationalRevolution.newGoals.item2": {"message": "从\"知道\"到\"成为\"：目标是培养能自主学习、解决复杂问题、做出伦理判断的\"终身学习者\""}, "ai101.educationalRevolution.newGoals.item3": {"message": "核心素养框架：各国均提出整合\"知识、思维、价值、实践\"的\"四位一体\"素养模型"}, "ai101.educationalRevolution.deconstructed.title": {"message": "解构的课堂：课程与教学法"}, "ai101.educationalRevolution.deconstructed.item1": {"message": "教学法转向：以项目式学习（PBL）、探究式学习为核心"}, "ai101.educationalRevolution.deconstructed.item2": {"message": "课程重构：从学科本位转向素养本位，强调跨学科整合和真实问题解决"}, "ai101.educationalRevolution.deconstructed.item3": {"message": "学习空间变革：从固定教室转向灵活学习环境，支持个性化和协作学习"}, "ai618.outline.title": {"message": "大纲"}, "ai618.outline.item1": {"message": "生成式 AI 关键技术及特征"}, "ai618.outline.item2": {"message": "人工智能时代的人机协作"}, "ai618.outline.item3": {"message": "人工智能时代的教育革命"}, "ai618.outline.item4": {"message": "人工智能与高阶思维能力培养"}, "ai618.outline.item5": {"message": "案例与操作"}, "ai618.section1.title": {"message": "生成式 AI 关键技术及特征"}, "ai618.section1.subtitle": {"message": "理解核心技术与特征"}, "ai618.section2.title": {"message": "人工智能时代的人机协作"}, "ai618.section2.subtitle": {"message": "AI时代的人机协作模式"}, "ai618.section3.title": {"message": "人工智能时代的教育革命"}, "ai618.section3.subtitle": {"message": "AI时代的教育变革"}, "ai618.section4.title": {"message": "人工智能与高阶思维能力培养"}, "ai618.section4.subtitle": {"message": "AI与高阶思维技能发展"}, "ai618.section5.title": {"message": "案例与操作"}, "ai618.section5.subtitle": {"message": "实际案例与操作实践"}, "ai101.teacherRoles.title": {"message": "教师新角色与评价新范式"}, "ai101.teacherRoles.subtitle": {"message": "从知识传授者到学习体验设计师"}, "ai101.teacherRoles.designer.title": {"message": "教师作为\"学习设计师\""}, "ai101.teacherRoles.designer.item1": {"message": "角色重塑：从\"知识传授者\"到\"学习体验的促进者、引导者与设计师\""}, "ai101.teacherRoles.designer.item2": {"message": "价值跃迁：AI接管重复性工作（如批改、答疑），教师得以专注于思想启迪、情感关怀等高价值互动"}, "ai101.teacherRoles.designer.item3": {"message": "能力要求：必须具备高水平的AI素养和教学设计能力，教师培训是关键"}, "ai101.teacherRoles.assessment.title": {"message": "为成长而评价：超越标准化考试"}, "ai101.teacherRoles.assessment.item1": {"message": "功能转型：从\"选拔与排序\"转向\"诊断与赋能\""}, "ai101.teacherRoles.assessment.item2": {"message": "方法多元化：采用成长记录袋、表现性评价、多元主体评价等方式"}, "ai101.teacherRoles.assessment.item3": {"message": "AI辅助：利用AI生成\"学生数字画像\"，动态追踪素养发展，提供形成性反馈"}, "ai101.aiTools.title": {"message": "新工具：AI驱动的教育产品生态"}, "ai101.aiTools.subtitle": {"message": "从\"内容分发器\"到\"认知合作伙伴\""}, "ai101.aiTools.adaptive.title": {"message": "智能辅导与自适应学习平台"}, "ai101.aiTools.adaptive.content": {"message": "实现大规模\"因材施教\""}, "ai101.aiTools.copilot.title": {"message": "教师与管理者\"副驾驶\""}, "ai101.aiTools.copilot.content": {"message": "AI辅助备课、出题、排课，为教师减负增效"}, "ai101.aiTools.immersive.title": {"message": "沉浸式与体验式学习环境"}, "ai101.aiTools.immersive.content": {"message": "VR/AR虚拟实验、历史场景重现，提升学习趣味性"}, "ai101.aiTools.content.title": {"message": "内容交互与分析工具"}, "ai101.aiTools.content.content": {"message": "与PDF文档对话、总结要点、整理笔记"}, "ai101.aiTools.collaborative.title": {"message": "协同学习支持平台"}, "ai101.aiTools.collaborative.content": {"message": "AI辅助智能分组、引导讨论，增强团队协作"}, "ai101.aiTools.thinking.title": {"message": "\"认知健身房\"：主动促进批判性与创造力"}, "ai101.aiTools.thinking.content": {"message": "制造并管理\"有益的认知摩擦\"，而非一味追求便利，以防\"认知外包\""}, "ai101.riskManagement.title": {"message": "防风险：驾驭双刃剑"}, "ai101.riskManagement.subtitle": {"message": "规避认知外包与构建共治模型"}, "ai101.riskManagement.cognitiveOutsourcing.title": {"message": "认知外包风险"}, "ai101.riskManagement.cognitiveOutsourcing.item1": {"message": "悖论：AI既是\"认知健身房\"，也可能是\"认知拐杖\""}, "ai101.riskManagement.cognitiveOutsourcing.item2": {"message": "关键变量：影响好坏的不是AI工具本身，而是其所处的\"教学法情境\""}, "ai101.riskManagement.cognitiveOutsourcing.item3": {"message": "应对之道：对教师进行系统性培训，使其掌握能够利用AI促进高阶思维的教学设计能力"}, "ai101.strategicRecommendations.title": {"message": "谋未来：战略建议与行动路线"}, "ai101.strategicRecommendations.subtitle": {"message": "迈向AI就绪未来的战略建议"}, "ai101.strategicRecommendations.policyMakers.title": {"message": "对国家及区域政策制定者"}, "ai101.strategicRecommendations.policyMakers.item1": {"message": "制定国家级\"AI与教育\"整合战略，系统性布局"}, "ai101.strategicRecommendations.policyMakers.item2": {"message": "发起\"未来技能\"式全民终身学习计划，支持劳动力转型"}, "ai101.strategicRecommendations.policyMakers.item3": {"message": "建立敏捷的AI教育治理框架，采用\"监管沙盒\"等自适应模式"}, "ai101.strategicRecommendations.educators.title": {"message": "对教育机构领导者"}, "ai101.strategicRecommendations.educators.item1": {"message": "将教师专业发展置于战略核心，支持教师向\"学习设计师\"转型"}, "ai101.strategicRecommendations.educators.item2": {"message": "推动课程与评价的系统性重构，从\"知识本位\"转向\"素养本位\""}, "ai101.strategicRecommendations.educators.item3": {"message": "采纳严格的AI产品伦理采购框架，将教育价值与伦理置于首位"}, "ai_mindmap.head.title": {"message": "FunBlocks AI思维导图插件 - 将网页内容转化为思维导图"}, "ai_mindmap.head.description": {"message": "使用AI将任何网页或YouTube视频转化为可视化思维导图。一键头脑风暴、批判性思维，与FunBlocks AIFlow无缝集成。免费Chrome插件。"}, "ai_mindmap.head.keywords": {"message": "AI思维导图, 思维导图, Chrome插件, 网页内容分析, YouTube字幕, 头脑风暴, 批判性思维, AIFlow, 可视化学习, 知识映射"}, "ai_mindmap.hero.badge": {"message": "全新CHROME插件"}, "ai_mindmap.hero.title": {"message": "用AI将任何网页内容转化为思维导图"}, "ai_mindmap.hero.subtitle": {"message": "一键从网页、YouTube视频和AI对话生成思维导图。您通往FunBlocks AIFlow的门户。"}, "ai_mindmap.hero.download": {"message": "免费下载插件"}, "ai_mindmap.hero.learn_more": {"message": "了解工作原理"}, "ai_mindmap.hero.stat1": {"message": "免费思维导图"}, "ai_mindmap.hero.stat2": {"message": "每日免费使用"}, "ai_mindmap.hero.stat3": {"message": "用户评分"}, "ai_mindmap.hero.image_caption": {"message": "点击放大"}, "ai_mindmap.benefits.title": {"message": "主要优势"}, "ai_mindmap.benefits.description": {"message": "发现FunBlocks AI思维导图插件如何通过这些强大优势提升您的浏览体验："}, "ai_mindmap.benefits.benefit1.title": {"message": "视觉学习增强"}, "ai_mindmap.benefits.benefit1.description": {"message": "将复杂信息转化为可视化思维导图，提高理解力和记忆保持力。"}, "ai_mindmap.benefits.benefit2.title": {"message": "即时知识提取"}, "ai_mindmap.benefits.benefit2.description": {"message": "在几秒钟内从网页和视频中提取关键洞察，节省数小时的手动笔记时间。"}, "ai_mindmap.benefits.benefit3.title": {"message": "增强专注力和清晰度"}, "ai_mindmap.benefits.benefit3.description": {"message": "将分散的信息组织成清晰、结构化的思维导图，突出重要连接。"}, "ai_mindmap.benefits.benefit4.title": {"message": "无缝工作流集成"}, "ai_mindmap.benefits.benefit4.description": {"message": "从任何网页内容开始，无缝过渡到FunBlocks AIFlow中的深度探索。"}, "ai_mindmap.benefits.benefit5.title": {"message": "AI驱动的洞察"}, "ai_mindmap.benefits.benefit5.description": {"message": "利用AI发现隐藏模式，从现有内容中生成新想法。"}, "ai_mindmap.benefits.benefit6.title": {"message": "随时可用"}, "ai_mindmap.benefits.benefit6.description": {"message": "无论您在哪里浏览，无论何时灵感来袭，都能访问强大的思维导图功能。"}, "ai_mindmap.how_it_works.title": {"message": "AI思维导图工作原理"}, "ai_mindmap.how_it_works.description": {"message": "只需一键即可将任何网页内容转化为可视化思维导图。非常适合学习、研究和创意思考。"}, "ai_mindmap.how_it_works.step1.title": {"message": "安装插件"}, "ai_mindmap.how_it_works.step1.description": {"message": "几秒钟内将AI思维导图添加到Chrome。无需复杂设置。"}, "ai_mindmap.how_it_works.step2.title": {"message": "浏览任何网站"}, "ai_mindmap.how_it_works.step2.description": {"message": "访问任何网页或YouTube视频，点击AI思维导图图标提取内容。"}, "ai_mindmap.how_it_works.step3.title": {"message": "生成思维导图"}, "ai_mindmap.how_it_works.step3.description": {"message": "观看AI瞬间将复杂内容转化为清晰的可视化思维导图。"}, "ai_mindmap.how_it_works.feature1.badge": {"message": "网页内容"}, "ai_mindmap.how_it_works.feature1.title": {"message": "一键网页思维导图"}, "ai_mindmap.how_it_works.feature1.description": {"message": "将任何文章、博客文章或网页转化为结构化思维导图。非常适合研究、学习和内容分析。"}, "ai_mindmap.how_it_works.feature1.point1": {"message": "自动从网页提取关键概念和主要思想"}, "ai_mindmap.how_it_works.feature1.point2": {"message": "分层组织信息以便更好理解"}, "ai_mindmap.how_it_works.feature1.point3": {"message": "保存到FunBlocks AIFlow进行进一步探索和开发"}, "ai_mindmap.how_it_works.feature1.image_caption": {"message": "点击放大"}, "ai_mindmap.how_it_works.feature2.badge": {"message": "YOUTUBE视频"}, "ai_mindmap.how_it_works.feature2.title": {"message": "YouTube视频思维导图"}, "ai_mindmap.how_it_works.feature2.description": {"message": "将YouTube视频字幕转化为可视化思维导图。非常适合教育视频、讲座和教程。"}, "ai_mindmap.how_it_works.feature2.point1": {"message": "自动从视频字幕提取关键要点"}, "ai_mindmap.how_it_works.feature2.point2": {"message": "非常适合教育内容和在线学习"}, "ai_mindmap.how_it_works.feature2.point3": {"message": "通过即时可视化摘要节省数小时的笔记时间"}, "ai_mindmap.how_it_works.feature2.image_caption": {"message": "点击放大"}, "ai_mindmap.how_it_works.feature3.badge": {"message": "AI头脑风暴"}, "ai_mindmap.how_it_works.feature3.title": {"message": "AI驱动的头脑风暴和分析"}, "ai_mindmap.how_it_works.feature3.description": {"message": "从任何主题生成思维导图，进行批判性分析，在AI协助下探索复杂主题。"}, "ai_mindmap.how_it_works.feature3.point1": {"message": "结合经典思维模型的AI头脑风暴"}, "ai_mindmap.how_it_works.feature3.point2": {"message": "批判性分析和决策支持"}, "ai_mindmap.how_it_works.feature3.point3": {"message": "分解复杂主题以便更好理解"}, "ai_mindmap.how_it_works.feature3.image_caption": {"message": "点击放大"}, "ai_mindmap.aiflow_integration.title": {"message": "与FunBlocks AIFlow无缝集成"}, "ai_mindmap.aiflow_integration.description": {"message": "AI思维导图作为您进入强大FunBlocks AIFlow生态系统的快速入口。从任何地方开始，到处探索。"}, "ai_mindmap.aiflow_integration.gateway.title": {"message": "您的AIFlow门户"}, "ai_mindmap.aiflow_integration.gateway.description": {"message": "将网页浏览转化为主动学习。您创建的每个思维导图都可以保存并在FunBlocks AIFlow中扩展，进行更深入的探索。"}, "ai_mindmap.aiflow_integration.gateway.point1": {"message": "一键保存到FunBlocks AIFlow工作空间"}, "ai_mindmap.aiflow_integration.gateway.point2": {"message": "从浏览器到完整AIFlow体验的无缝过渡"}, "ai_mindmap.aiflow_integration.gateway.point3": {"message": "使用高级思维工具继续探索"}, "ai_mindmap.aiflow_integration.gateway.image_caption": {"message": "点击放大"}, "ai_mindmap.cta.title": {"message": "今天就开启您的可视化学习之旅"}, "ai_mindmap.cta.description": {"message": "通过AI驱动的思维导图，改变您的学习、研究和思考方式。加入已经提升学习体验的数千名用户。"}, "ai_mindmap.cta.download": {"message": "下载扩展"}, "ai_mindmap.cta.explore": {"message": "探索 AIFlow"}, "ai_mindmap.cta.note": {"message": "✨ 免费使用 • 无需信用卡 • 秒速安装"}, "ai_mindmap.testimonials.title": {"message": "用户反馈"}, "ai_mindmap.testimonials.description": {"message": "加入数千名正在改变学习和工作方式的满意用户"}, "ai_mindmap.testimonials.user1.name": {"message": "陈大伟"}, "ai_mindmap.testimonials.user1.role": {"message": "研究生"}, "ai_mindmap.testimonials.user1.text": {"message": "这个扩展对研究工作来说是个革命性的工具！我用它从学术论文和讲座视频中创建思维导图。AI提取关键概念的能力令人印象深刻。"}, "ai_mindmap.testimonials.user2.name": {"message": "莎拉·约翰逊"}, "ai_mindmap.testimonials.user2.role": {"message": "内容创作者"}, "ai_mindmap.testimonials.user2.text": {"message": "FunBlocks AI思维导图彻底改变了我组织内容想法的方式。从网络研究中即时生成思维导图每周为我节省数小时工作时间！"}, "ai_mindmap.testimonials.user3.name": {"message": "迈克尔·布朗教授"}, "ai_mindmap.testimonials.user3.role": {"message": "大学讲师"}, "ai_mindmap.testimonials.user3.text": {"message": "我向所有学生推荐这个工具。它帮助他们将复杂的主题可视化，提高了对课程材料的理解。"}, "ai_mindmap.testimonials.user4.name": {"message": "张艾玛"}, "ai_mindmap.testimonials.user4.role": {"message": "高中生"}, "ai_mindmap.testimonials.user4.text": {"message": "太喜欢它把YouTube教育视频转换成思维导图的功能了！让学习变得更轻松有趣。"}, "ai_mindmap.testimonials.user5.name": {"message": "詹姆斯·威尔逊"}, "ai_mindmap.testimonials.user5.role": {"message": "商业分析师"}, "ai_mindmap.testimonials.user5.text": {"message": "非常适合分析市场研究和竞争对手网站。与AIFlow的集成使其成为强大的商业智能工具。"}, "ai_mindmap.testimonials.user6.name": {"message": "王丽莎博士"}, "ai_mindmap.testimonials.user6.role": {"message": "研究科学家"}, "ai_mindmap.testimonials.user6.text": {"message": "AI识别概念之间联系的能力令人惊叹。它已经成为我进行文献综述的必备工具。"}, "ai_mindmap.faq.q1": {"message": "什么是FunBlocks AI思维导图插件？"}, "ai_mindmap.faq.a1": {"message": "FunBlocks AI思维导图插件是一个Chrome浏览器插件，使用AI将任何网页或YouTube视频转化为可视化思维导图。它通过提取关键概念并以易于理解的可视化格式呈现，帮助学习、研究和知识组织。"}, "ai_mindmap.faq.q2": {"message": "AI思维导图插件如何工作？"}, "ai_mindmap.faq.a2": {"message": "只需安装插件，访问任何网页或YouTube视频，然后点击AI思维导图图标。插件会自动提取关键概念并创建结构化思维导图，您可以将其保存到FunBlocks AIFlow进行进一步探索。"}, "ai_mindmap.faq.q3": {"message": "我可以在YouTube视频上使用这个插件吗？"}, "ai_mindmap.faq.a3": {"message": "可以！插件可以提取YouTube视频的字幕并将其转化为思维导图，非常适合教育内容、讲座和教程。这为您节省了数小时的手动笔记时间。"}, "ai_mindmap.faq.q4": {"message": "AI思维导图插件免费吗？"}, "ai_mindmap.faq.a4": {"message": "是的，插件免费下载和使用。新用户可获得30次免费思维导图生成，每天还有10次免费使用额度。所有FunBlocks AI订阅计划都包含无限使用。"}, "ai_mindmap.faq.q5": {"message": "它如何与FunBlocks AIFlow集成？"}, "ai_mindmap.faq.a5": {"message": "AI思维导图插件作为FunBlocks AIFlow的门户。您可以将任何思维导图直接保存到AIFlow工作空间，进行更深入的探索、头脑风暴，并访问高级思维工具，如批判性分析和创意思维框架。"}, "ai_mindmap.faq.q6": {"message": "支持哪些浏览器？"}, "ai_mindmap.faq.a6": {"message": "目前，插件支持Chrome和Edge浏览器。我们计划根据用户需求扩展到其他浏览器。"}, "ai_mindmap.faq.q7": {"message": "我可以用插件进行新想法的头脑风暴吗？"}, "ai_mindmap.faq.a7": {"message": "当然可以！除了分析现有内容，插件还包含AI驱动的头脑风暴工具，可以生成任何主题的思维导图，进行批判性分析，并使用经典思维模型帮助决策。"}, "ai_mindmap.faq.q8": {"message": "这与其他思维导图工具有什么不同？"}, "ai_mindmap.faq.a8": {"message": "我们的插件独特地结合了AI驱动的内容提取、无缝浏览器集成和与强大FunBlocks AIFlow生态系统的直接连接。它不仅仅是一个思维导图工具——它是您在AI时代增强思维和学习的门户。"}}