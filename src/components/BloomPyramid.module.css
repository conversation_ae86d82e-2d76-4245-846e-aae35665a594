.bloomPyramid {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.pyramidTitle {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
}

.pyramidContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.pyramidLevel {
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  border: 2px solid;
  min-height: 60px;
  color: #333;
}

.pyramidLevel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.levelTitle {
  font-size: 1.1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.levelIcon {
  font-size: 1.2rem;
}

.levelDescription {
  font-size: 0.85rem;
  font-weight: 400;
  color: #666;
  margin: 0;
}

/* Pyramid shape - CREATE at top (smallest), REMEMBER at bottom (largest) */
.level1 { 
  width: 200px; 
  background: #d6d8db;
  border-color: #999;
}

.level2 { 
  width: 300px; 
  background: #e8eaed;
  border-color: #999;
}

.level3 { 
  width: 400px; 
  background: #f1f3f4;
  border-color: #999;
}

.level4 { 
  width: 500px; 
  background: #ffffff;
  border-color: #ccc;
}

.level5 { 
  width: 600px; 
  background: #ffffff;
  border-color: #ccc;
}

.level6 { 
  width: 700px; 
  background: #ffffff;
  border-color: #ccc;
}

.pyramidFooter {
  text-align: center;
  margin-top: 2rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
  font-style: italic;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .bloomPyramid {
    padding: 1.5rem;
  }
  
  .level1 { width: 150px; }
  .level2 { width: 200px; }
  .level3 { width: 250px; }
  .level4 { width: 300px; }
  .level5 { width: 350px; }
  .level6 { width: 400px; }
  
  .levelTitle {
    font-size: 1rem;
  }
  
  .levelDescription {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .level1 { width: 120px; }
  .level2 { width: 160px; }
  .level3 { width: 200px; }
  .level4 { width: 240px; }
  .level5 { width: 280px; }
  .level6 { width: 320px; }
}
