import React from 'react';
import Translate from '@docusaurus/Translate';
import styles from './BloomPyramid.module.css';

export default function BloomPyramid() {
  const levels = [
    {
      id: 'create',
      icon: '💡',
      titleId: 'education.future.thinking.bloom.create',
      descId: 'education.future.thinking.bloom.create.desc'
    },
    {
      id: 'evaluate',
      icon: '⚖️',
      titleId: 'education.future.thinking.bloom.evaluate',
      descId: 'education.future.thinking.bloom.evaluate.desc'
    },
    {
      id: 'analyze',
      icon: '🔍',
      titleId: 'education.future.thinking.bloom.analyze',
      descId: 'education.future.thinking.bloom.analyze.desc'
    },
    {
      id: 'apply',
      icon: '🛠️',
      titleId: 'education.future.thinking.bloom.apply',
      descId: 'education.future.thinking.bloom.apply.desc'
    },
    {
      id: 'understand',
      icon: '💭',
      titleId: 'education.future.thinking.bloom.understand',
      descId: 'education.future.thinking.bloom.understand.desc'
    },
    {
      id: 'remember',
      icon: '📚',
      titleId: 'education.future.thinking.bloom.remember',
      descId: 'education.future.thinking.bloom.remember.desc'
    }
  ];

  return (
    <div className={styles.bloomPyramid}>
      <h3 className={styles.pyramidTitle}>
        <Translate id="education.future.thinking.bloom.title">
          Bloom's Taxonomy Pyramid
        </Translate>
      </h3>
      
      <div className={styles.pyramidContainer}>
        {levels.map((level, index) => (
          <div key={level.id} className={`${styles.pyramidLevel} ${styles[`level${index + 1}`]}`}>
            <div className={styles.levelTitle}>
              <span className={styles.levelIcon}>{level.icon}</span>
              <Translate id={level.titleId} />
            </div>
            <div className={styles.levelDescription}>
              <Translate id={level.descId} />
            </div>
          </div>
        ))}
      </div>
      
      <div className={styles.pyramidFooter}>
        <Translate id="education.future.thinking.bloom.subtitle">
          Cognitive complexity increases from bottom to top
        </Translate>
      </div>
    </div>
  );
}
